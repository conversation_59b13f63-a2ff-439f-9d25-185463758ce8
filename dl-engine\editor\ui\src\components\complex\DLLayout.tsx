/**
 * DL-Engine 布局组件
 * 
 * 基于 Ant Design Layout 的增强版本，支持响应式布局
 */

import React from 'react'
import { Layout, LayoutProps } from 'antd'
import classNames from 'classnames'

const { Header, Footer, Sider, Content } = Layout

/**
 * 布局方向
 */
export type DLLayoutDirection = 'horizontal' | 'vertical'

/**
 * DL布局属性
 */
export interface DLLayoutProps extends LayoutProps {
  /** 布局方向 */
  direction?: DLLayoutDirection
  /** 是否填充父容器 */
  fill?: boolean
  /** 是否响应式 */
  responsive?: boolean
  /** 自定义类名 */
  className?: string
  /** 子组件 */
  children?: React.ReactNode
}

/**
 * DL侧边栏属性
 */
export interface DLSiderProps extends React.ComponentProps<typeof Sider> {
  /** 是否可调整大小 */
  resizable?: boolean
  /** 最小宽度 */
  minWidth?: number
  /** 最大宽度 */
  maxWidth?: number
  /** 自定义类名 */
  className?: string
}

/**
 * DL头部属性
 */
export interface DLHeaderProps extends React.ComponentProps<typeof Header> {
  /** 是否固定 */
  fixed?: boolean
  /** 自定义类名 */
  className?: string
}

/**
 * DL底部属性
 */
export interface DLFooterProps extends React.ComponentProps<typeof Footer> {
  /** 是否固定 */
  fixed?: boolean
  /** 自定义类名 */
  className?: string
}

/**
 * DL内容属性
 */
export interface DLContentProps extends React.ComponentProps<typeof Content> {
  /** 是否有内边距 */
  padding?: boolean
  /** 自定义类名 */
  className?: string
}

/**
 * DL布局组件
 */
const DLLayout: React.FC<DLLayoutProps> & {
  Header: React.FC<DLHeaderProps>
  Footer: React.FC<DLFooterProps>
  Sider: React.FC<DLSiderProps>
  Content: React.FC<DLContentProps>
} = ({
  direction = 'horizontal',
  fill = true,
  responsive = true,
  className,
  children,
  ...props
}) => {
  /**
   * 布局类名
   */
  const layoutClassName = classNames(
    'dl-layout',
    {
      'dl-layout--horizontal': direction === 'horizontal',
      'dl-layout--vertical': direction === 'vertical',
      'dl-layout--fill': fill,
      'dl-layout--responsive': responsive
    },
    className
  )
  
  return (
    <Layout
      {...props}
      className={layoutClassName}
    >
      {children}
    </Layout>
  )
}

/**
 * DL头部组件
 */
const DLHeader: React.FC<DLHeaderProps> = ({
  fixed = false,
  className,
  children,
  ...props
}) => {
  const headerClassName = classNames(
    'dl-header',
    {
      'dl-header--fixed': fixed
    },
    className
  )
  
  return (
    <Header
      {...props}
      className={headerClassName}
    >
      {children}
    </Header>
  )
}

/**
 * DL底部组件
 */
const DLFooter: React.FC<DLFooterProps> = ({
  fixed = false,
  className,
  children,
  ...props
}) => {
  const footerClassName = classNames(
    'dl-footer',
    {
      'dl-footer--fixed': fixed
    },
    className
  )
  
  return (
    <Footer
      {...props}
      className={footerClassName}
    >
      {children}
    </Footer>
  )
}

/**
 * DL侧边栏组件
 */
const DLSider: React.FC<DLSiderProps> = ({
  resizable = false,
  minWidth = 200,
  maxWidth = 600,
  className,
  children,
  ...props
}) => {
  const siderClassName = classNames(
    'dl-sider',
    {
      'dl-sider--resizable': resizable
    },
    className
  )
  
  return (
    <Sider
      {...props}
      className={siderClassName}
      style={{
        minWidth: resizable ? minWidth : undefined,
        maxWidth: resizable ? maxWidth : undefined,
        ...props.style
      }}
    >
      {children}
    </Sider>
  )
}

/**
 * DL内容组件
 */
const DLContent: React.FC<DLContentProps> = ({
  padding = true,
  className,
  children,
  ...props
}) => {
  const contentClassName = classNames(
    'dl-content',
    {
      'dl-content--padding': padding
    },
    className
  )
  
  return (
    <Content
      {...props}
      className={contentClassName}
    >
      {children}
    </Content>
  )
}

// 绑定子组件
DLLayout.Header = DLHeader
DLLayout.Footer = DLFooter
DLLayout.Sider = DLSider
DLLayout.Content = DLContent

export default DLLayout
