/**
 * XR UI文本组件
 * 
 * 在3D空间中显示文本内容
 */

import { Object3D, Vector3 } from 'three'
import { WebLayer3D } from './WebLayer3D'

export interface XRUITextOptions {
  /** 文本内容 */
  text?: string
  /** 文本宽度（米） */
  width?: number
  /** 文本高度（米） */
  height?: number
  /** 文本位置 */
  position?: Vector3
  /** 字体大小 */
  fontSize?: number
  /** 字体粗细 */
  fontWeight?: 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900'
  /** 字体系列 */
  fontFamily?: string
  /** 文本颜色 */
  color?: string
  /** 背景颜色 */
  backgroundColor?: string
  /** 文本对齐 */
  textAlign?: 'left' | 'center' | 'right' | 'justify'
  /** 垂直对齐 */
  verticalAlign?: 'top' | 'middle' | 'bottom'
  /** 行高 */
  lineHeight?: number
  /** 内边距 */
  padding?: number
  /** 是否自动换行 */
  wordWrap?: boolean
  /** 最大行数 */
  maxLines?: number
  /** 文本阴影 */
  textShadow?: string
  /** 是否可选择 */
  selectable?: boolean
}

/**
 * XR UI文本类
 */
export class XRUIText extends Object3D {
  private options: Required<XRUITextOptions>
  private webLayer: WebLayer3D
  private textElement: HTMLDivElement

  constructor(options: XRUITextOptions = {}) {
    super()

    // 设置默认选项
    this.options = {
      text: 'Text',
      width: 0.5,
      height: 0.1,
      position: new Vector3(0, 0, 0),
      fontSize: 16,
      fontWeight: 'normal',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
      color: '#ffffff',
      backgroundColor: 'transparent',
      textAlign: 'left',
      verticalAlign: 'middle',
      lineHeight: 1.4,
      padding: 8,
      wordWrap: true,
      maxLines: 0,
      textShadow: 'none',
      selectable: false,
      ...options
    }

    this.createText()
    this.updateTransform()
  }

  /**
   * 创建文本DOM结构
   */
  private createText(): void {
    // 创建文本元素
    this.textElement = document.createElement('div')
    this.textElement.textContent = this.options.text
    
    this.updateTextStyle()

    // 创建WebLayer3D
    this.webLayer = new WebLayer3D(this.textElement, {
      pixelWidth: 512,
      pixelHeight: Math.round(512 * (this.options.height / this.options.width))
    })

    this.webLayer.scale.set(this.options.width, this.options.height, 1)
    this.add(this.webLayer)
  }

  /**
   * 更新文本样式
   */
  private updateTextStyle(): void {
    const displayFlex = this.options.verticalAlign !== 'top'
    const alignItems = this.getAlignItems()
    const justifyContent = this.getJustifyContent()

    this.textElement.style.cssText = `
      width: 100%;
      height: 100%;
      background-color: ${this.options.backgroundColor};
      color: ${this.options.color};
      font-size: ${this.options.fontSize}px;
      font-weight: ${this.options.fontWeight};
      font-family: ${this.options.fontFamily};
      text-align: ${this.options.textAlign};
      line-height: ${this.options.lineHeight};
      padding: ${this.options.padding}px;
      box-sizing: border-box;
      word-wrap: ${this.options.wordWrap ? 'break-word' : 'normal'};
      text-shadow: ${this.options.textShadow};
      user-select: ${this.options.selectable ? 'text' : 'none'};
      overflow: hidden;
      ${displayFlex ? `
        display: flex;
        align-items: ${alignItems};
        justify-content: ${justifyContent};
      ` : ''}
      ${this.options.maxLines > 0 ? `
        display: -webkit-box;
        -webkit-line-clamp: ${this.options.maxLines};
        -webkit-box-orient: vertical;
      ` : ''}
    `
  }

  /**
   * 获取垂直对齐样式
   */
  private getAlignItems(): string {
    switch (this.options.verticalAlign) {
      case 'top': return 'flex-start'
      case 'middle': return 'center'
      case 'bottom': return 'flex-end'
      default: return 'center'
    }
  }

  /**
   * 获取水平对齐样式
   */
  private getJustifyContent(): string {
    switch (this.options.textAlign) {
      case 'left': return 'flex-start'
      case 'center': return 'center'
      case 'right': return 'flex-end'
      case 'justify': return 'space-between'
      default: return 'flex-start'
    }
  }

  /**
   * 更新变换
   */
  private updateTransform(): void {
    this.position.copy(this.options.position)
  }

  /**
   * 设置文本内容
   */
  setText(text: string): void {
    this.options.text = text
    this.textElement.textContent = text
  }

  /**
   * 获取文本内容
   */
  getText(): string {
    return this.options.text
  }

  /**
   * 设置HTML内容
   */
  setHTML(html: string): void {
    this.textElement.innerHTML = html
  }

  /**
   * 设置文本位置
   */
  setPosition(position: Vector3): void {
    this.options.position.copy(position)
    this.updateTransform()
  }

  /**
   * 设置文本大小
   */
  setSize(width: number, height: number): void {
    this.options.width = width
    this.options.height = height
    this.webLayer.scale.set(width, height, 1)
  }

  /**
   * 设置字体大小
   */
  setFontSize(fontSize: number): void {
    this.options.fontSize = fontSize
    this.updateTextStyle()
  }

  /**
   * 设置字体粗细
   */
  setFontWeight(fontWeight: XRUITextOptions['fontWeight']): void {
    this.options.fontWeight = fontWeight!
    this.updateTextStyle()
  }

  /**
   * 设置文本颜色
   */
  setColor(color: string): void {
    this.options.color = color
    this.updateTextStyle()
  }

  /**
   * 设置背景颜色
   */
  setBackgroundColor(backgroundColor: string): void {
    this.options.backgroundColor = backgroundColor
    this.updateTextStyle()
  }

  /**
   * 设置文本对齐
   */
  setTextAlign(textAlign: XRUITextOptions['textAlign']): void {
    this.options.textAlign = textAlign!
    this.updateTextStyle()
  }

  /**
   * 设置垂直对齐
   */
  setVerticalAlign(verticalAlign: XRUITextOptions['verticalAlign']): void {
    this.options.verticalAlign = verticalAlign!
    this.updateTextStyle()
  }

  /**
   * 设置行高
   */
  setLineHeight(lineHeight: number): void {
    this.options.lineHeight = lineHeight
    this.updateTextStyle()
  }

  /**
   * 设置内边距
   */
  setPadding(padding: number): void {
    this.options.padding = padding
    this.updateTextStyle()
  }

  /**
   * 设置自动换行
   */
  setWordWrap(wordWrap: boolean): void {
    this.options.wordWrap = wordWrap
    this.updateTextStyle()
  }

  /**
   * 设置最大行数
   */
  setMaxLines(maxLines: number): void {
    this.options.maxLines = maxLines
    this.updateTextStyle()
  }

  /**
   * 设置可选择状态
   */
  setSelectable(selectable: boolean): void {
    this.options.selectable = selectable
    this.updateTextStyle()
  }

  /**
   * 获取文本元素
   */
  getElement(): HTMLDivElement {
    return this.textElement
  }

  /**
   * 销毁文本
   */
  dispose(): void {
    if (this.webLayer) {
      this.webLayer.dispose()
    }
    if (this.parent) {
      this.parent.remove(this)
    }
  }
}

export default XRUIText
