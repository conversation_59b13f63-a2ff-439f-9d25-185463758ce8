/**
 * 节点渲染器组件
 * 
 * 渲染单个节点的外观和交互
 */

import React, { useCallback, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'
import SocketRenderer from './SocketRenderer'
import { NodeInstance, Vector2, NodeSocket } from '../types'

/**
 * 节点渲染器属性
 */
export interface NodeRendererProps {
  /** 节点实例 */
  node: NodeInstance
  /** 是否选中 */
  selected: boolean
  /** 是否正在执行 */
  executing: boolean
  /** 是否只读 */
  readonly: boolean
  /** 拖拽回调 */
  onDrag?: (delta: Vector2) => void
  /** 连接开始回调 */
  onConnectionStart?: (socketId: string, socketType: 'input' | 'output') => void
  /** 连接结束回调 */
  onConnectionEnd?: (socketId: string, socketType: 'input' | 'output') => void
  /** 选择回调 */
  onSelect?: () => void
  /** 自定义类名 */
  className?: string
}

/**
 * 节点渲染器组件
 */
const NodeRenderer: React.FC<NodeRendererProps> = ({
  node,
  selected,
  executing,
  readonly,
  onDrag,
  onConnectionStart,
  onConnectionEnd,
  onSelect,
  className = ''
}) => {
  const { t } = useTranslation()
  const nodeRef = useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState<Vector2>({ x: 0, y: 0 })
  
  /**
   * 处理鼠标按下
   */
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    if (readonly || event.button !== 0) return
    
    event.stopPropagation()
    setIsDragging(true)
    setDragStart({ x: event.clientX, y: event.clientY })
    onSelect?.()
  }, [readonly, onSelect])
  
  /**
   * 处理鼠标移动
   */
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!isDragging || readonly) return
    
    const delta = {
      x: event.clientX - dragStart.x,
      y: event.clientY - dragStart.y
    }
    
    onDrag?.(delta)
    setDragStart({ x: event.clientX, y: event.clientY })
  }, [isDragging, readonly, dragStart, onDrag])
  
  /**
   * 处理鼠标抬起
   */
  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])
  
  /**
   * 渲染节点头部
   */
  const renderHeader = () => {
    return (
      <div className="node-header">
        {/* 节点图标 */}
        {node.definition.icon && (
          <div className="node-icon">
            {typeof node.definition.icon === 'string' ? (
              <span>{node.definition.icon}</span>
            ) : (
              node.definition.icon
            )}
          </div>
        )}
        
        {/* 节点标题 */}
        <div className="node-title">
          {node.definition.name}
        </div>
        
        {/* 执行状态指示器 */}
        {executing && (
          <div className="node-execution-indicator">
            <div className="animate-pulse w-2 h-2 bg-green-500 rounded-full"></div>
          </div>
        )}
      </div>
    )
  }
  
  /**
   * 渲染输入插槽
   */
  const renderInputSockets = () => {
    const inputs = node.definition.inputs || []
    
    return (
      <div className="node-inputs">
        {inputs.map((socket, index) => (
          <div key={socket.id} className="node-socket-row">
            <SocketRenderer
              socket={socket}
              nodeId={node.id}
              type="input"
              connected={false} // TODO: 检查连接状态
              onConnectionStart={() => onConnectionStart?.(socket.id, 'input')}
              onConnectionEnd={() => onConnectionEnd?.(socket.id, 'input')}
            />
            <div className="socket-label">{socket.name}</div>
          </div>
        ))}
      </div>
    )
  }
  
  /**
   * 渲染输出插槽
   */
  const renderOutputSockets = () => {
    const outputs = node.definition.outputs || []
    
    return (
      <div className="node-outputs">
        {outputs.map((socket, index) => (
          <div key={socket.id} className="node-socket-row">
            <div className="socket-label">{socket.name}</div>
            <SocketRenderer
              socket={socket}
              nodeId={node.id}
              type="output"
              connected={false} // TODO: 检查连接状态
              onConnectionStart={() => onConnectionStart?.(socket.id, 'output')}
              onConnectionEnd={() => onConnectionEnd?.(socket.id, 'output')}
            />
          </div>
        ))}
      </div>
    )
  }
  
  /**
   * 渲染节点内容
   */
  const renderContent = () => {
    // 如果节点有自定义渲染器，使用自定义渲染器
    if (node.definition.render) {
      return (
        <div className="node-custom-content">
          {node.definition.render(node.data, (data) => {
            // TODO: 更新节点数据
          })}
        </div>
      )
    }
    
    // 默认内容渲染
    return (
      <div className="node-content">
        {/* 输入参数 */}
        {node.definition.inputs?.some(input => !input.isExecution) && (
          <div className="node-parameters">
            {node.definition.inputs
              .filter(input => !input.isExecution)
              .map(input => (
                <div key={input.id} className="node-parameter">
                  <label className="parameter-label">{input.name}</label>
                  {/* TODO: 根据数据类型渲染不同的输入控件 */}
                  <input
                    type="text"
                    value={node.data[input.id] || input.defaultValue || ''}
                    onChange={(e) => {
                      // TODO: 更新节点数据
                    }}
                    disabled={readonly}
                    className="parameter-input"
                  />
                </div>
              ))}
          </div>
        )}
      </div>
    )
  }
  
  /**
   * 节点类名
   */
  const nodeClassName = classNames(
    'node-renderer',
    `node-type-${node.definition.type}`,
    {
      'node-selected': selected,
      'node-executing': executing,
      'node-dragging': isDragging,
      'node-readonly': readonly,
      'node-educational': node.definition.educational
    },
    className
  )
  
  return (
    <div
      ref={nodeRef}
      className={nodeClassName}
      style={{
        position: 'absolute',
        left: node.position.x,
        top: node.position.y,
        backgroundColor: node.definition.color || '#ffffff',
        border: selected ? '2px solid #1890ff' : '1px solid #d9d9d9',
        borderRadius: '8px',
        minWidth: '150px',
        boxShadow: selected ? '0 4px 12px rgba(24, 144, 255, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)',
        cursor: isDragging ? 'grabbing' : 'grab'
      }}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
    >
      {/* 节点头部 */}
      {renderHeader()}
      
      {/* 节点主体 */}
      <div className="node-body">
        {/* 输入插槽 */}
        {renderInputSockets()}
        
        {/* 节点内容 */}
        {renderContent()}
        
        {/* 输出插槽 */}
        {renderOutputSockets()}
      </div>
    </div>
  )
}

export default NodeRenderer
