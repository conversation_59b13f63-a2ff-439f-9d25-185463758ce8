/**
 * 插件加载器
 * 
 * 负责插件的动态加载和代码执行
 */

import * as path from 'path'
import * as fs from 'fs/promises'
import { PluginMetadata, Plugin } from './PluginAPI'

/**
 * 插件加载器配置
 */
export interface PluginLoaderConfig {
  /** 插件目录 */
  pluginDir: string
  /** 是否启用开发模式 */
  devMode: boolean
  /** 是否启用热重载 */
  hotReload: boolean
}

/**
 * 插件模块缓存
 */
interface PluginModuleCache {
  [pluginPath: string]: {
    module: any
    timestamp: number
    watchers?: fs.FSWatcher[]
  }
}

/**
 * 插件加载器类
 */
export class PluginLoader {
  private config: PluginLoaderConfig
  private moduleCache: PluginModuleCache = {}
  private fileWatchers: Map<string, fs.FSWatcher> = new Map()
  
  constructor(config: PluginLoaderConfig) {
    this.config = config
  }
  
  /**
   * 扫描插件目录
   */
  async scanPluginDirectory(): Promise<string[]> {
    const pluginPaths: string[] = []
    
    try {
      const entries = await fs.readdir(this.config.pluginDir, { withFileTypes: true })
      
      for (const entry of entries) {
        if (entry.isDirectory()) {
          const pluginPath = path.join(this.config.pluginDir, entry.name)
          const manifestPath = path.join(pluginPath, 'plugin.json')
          
          try {
            await fs.access(manifestPath)
            pluginPaths.push(pluginPath)
          } catch {
            // 没有 plugin.json 文件，跳过
          }
        }
      }
    } catch (error) {
      console.warn('扫描插件目录失败:', error)
    }
    
    return pluginPaths
  }
  
  /**
   * 加载插件元数据
   */
  async loadPluginMetadata(pluginPath: string): Promise<PluginMetadata> {
    const manifestPath = path.join(pluginPath, 'plugin.json')
    
    try {
      const manifestContent = await fs.readFile(manifestPath, 'utf-8')
      const metadata = JSON.parse(manifestContent) as PluginMetadata
      
      // 验证必需字段
      this.validateMetadata(metadata)
      
      // 设置默认值
      if (!metadata.main) {
        metadata.main = 'index.js'
      }
      
      return metadata
    } catch (error) {
      throw new Error(`加载插件元数据失败 ${pluginPath}: ${error.message}`)
    }
  }
  
  /**
   * 验证插件元数据
   */
  private validateMetadata(metadata: any): void {
    const requiredFields = ['id', 'name', 'version', 'type']
    
    for (const field of requiredFields) {
      if (!metadata[field]) {
        throw new Error(`插件元数据缺少必需字段: ${field}`)
      }
    }
    
    // 验证ID格式
    if (!/^[a-z0-9-_.]+$/.test(metadata.id)) {
      throw new Error('插件ID格式无效，只能包含小写字母、数字、连字符、下划线和点')
    }
    
    // 验证版本格式
    if (!/^\d+\.\d+\.\d+/.test(metadata.version)) {
      throw new Error('插件版本格式无效，应为 x.y.z 格式')
    }
  }
  
  /**
   * 加载插件代码
   */
  async loadPluginCode(pluginPath: string): Promise<typeof Plugin> {
    const metadata = await this.loadPluginMetadata(pluginPath)
    const mainFile = path.join(pluginPath, metadata.main || 'index.js')
    
    // 检查缓存
    if (this.moduleCache[pluginPath] && !this.config.devMode) {
      return this.moduleCache[pluginPath].module.default || this.moduleCache[pluginPath].module
    }
    
    try {
      // 检查文件是否存在
      await fs.access(mainFile)
      
      let module: any
      
      if (mainFile.endsWith('.js') || mainFile.endsWith('.mjs')) {
        // 加载 JavaScript 模块
        module = await this.loadJavaScriptModule(mainFile)
      } else if (mainFile.endsWith('.ts')) {
        // 加载 TypeScript 模块（需要编译）
        module = await this.loadTypeScriptModule(mainFile)
      } else {
        throw new Error(`不支持的文件类型: ${path.extname(mainFile)}`)
      }
      
      // 验证模块导出
      const PluginClass = module.default || module
      if (typeof PluginClass !== 'function') {
        throw new Error('插件必须导出一个类')
      }
      
      // 缓存模块
      this.moduleCache[pluginPath] = {
        module,
        timestamp: Date.now()
      }
      
      // 设置热重载监听
      if (this.config.hotReload && this.config.devMode) {
        this.setupHotReload(pluginPath, mainFile)
      }
      
      return PluginClass
      
    } catch (error) {
      throw new Error(`加载插件代码失败 ${pluginPath}: ${error.message}`)
    }
  }
  
  /**
   * 加载 JavaScript 模块
   */
  private async loadJavaScriptModule(filePath: string): Promise<any> {
    // 在浏览器环境中，我们需要使用动态导入
    if (typeof window !== 'undefined') {
      // 浏览器环境
      const moduleUrl = this.createModuleUrl(filePath)
      return await import(moduleUrl)
    } else {
      // Node.js 环境
      // 清除 require 缓存以支持热重载
      if (this.config.devMode) {
        delete require.cache[require.resolve(filePath)]
      }
      return require(filePath)
    }
  }
  
  /**
   * 加载 TypeScript 模块
   */
  private async loadTypeScriptModule(filePath: string): Promise<any> {
    // 在实际应用中，这里需要集成 TypeScript 编译器
    // 或者预编译 TypeScript 文件
    throw new Error('TypeScript 插件加载暂未实现')
  }
  
  /**
   * 创建模块 URL（用于浏览器环境）
   */
  private createModuleUrl(filePath: string): string {
    // 将文件路径转换为可访问的 URL
    // 这里需要根据实际的文件服务配置来实现
    const relativePath = path.relative(this.config.pluginDir, filePath)
    return `/plugins/${relativePath}`
  }
  
  /**
   * 设置热重载
   */
  private setupHotReload(pluginPath: string, mainFile: string): void {
    if (this.fileWatchers.has(pluginPath)) {
      return // 已经设置了监听
    }
    
    try {
      const watcher = fs.watch(path.dirname(mainFile), { recursive: true }, (eventType, filename) => {
        if (filename && (filename.endsWith('.js') || filename.endsWith('.ts'))) {
          console.log(`检测到插件文件变化: ${pluginPath}/${filename}`)
          this.invalidateCache(pluginPath)
        }
      })
      
      this.fileWatchers.set(pluginPath, watcher)
      
    } catch (error) {
      console.warn(`设置热重载失败 ${pluginPath}:`, error)
    }
  }
  
  /**
   * 使缓存失效
   */
  private invalidateCache(pluginPath: string): void {
    if (this.moduleCache[pluginPath]) {
      delete this.moduleCache[pluginPath]
      console.log(`插件缓存已清除: ${pluginPath}`)
    }
  }
  
  /**
   * 预加载插件资源
   */
  async preloadPluginAssets(pluginPath: string): Promise<void> {
    const metadata = await this.loadPluginMetadata(pluginPath)
    
    if (metadata.assets) {
      for (const asset of metadata.assets) {
        const assetPath = path.join(pluginPath, asset)
        
        try {
          await fs.access(assetPath)
          // 这里可以预加载资源，比如图片、样式文件等
          console.log(`预加载插件资源: ${assetPath}`)
        } catch (error) {
          console.warn(`插件资源不存在: ${assetPath}`)
        }
      }
    }
  }
  
  /**
   * 验证插件安全性
   */
  async validatePluginSecurity(pluginPath: string): Promise<boolean> {
    // 这里可以实现插件安全性检查
    // 比如检查代码签名、扫描恶意代码等
    
    try {
      const metadata = await this.loadPluginMetadata(pluginPath)
      
      // 检查权限请求是否合理
      if (metadata.permissions) {
        for (const permission of metadata.permissions) {
          if (!this.isPermissionAllowed(permission)) {
            console.warn(`插件请求了不被允许的权限: ${permission}`)
            return false
          }
        }
      }
      
      return true
    } catch (error) {
      console.error(`插件安全性验证失败 ${pluginPath}:`, error)
      return false
    }
  }
  
  /**
   * 检查权限是否被允许
   */
  private isPermissionAllowed(permission: string): boolean {
    // 这里可以实现权限策略
    // 比如某些权限需要用户确认，某些权限被禁止等
    return true // 简化实现，允许所有权限
  }
  
  /**
   * 获取插件大小
   */
  async getPluginSize(pluginPath: string): Promise<number> {
    let totalSize = 0
    
    const calculateSize = async (dirPath: string): Promise<void> => {
      const entries = await fs.readdir(dirPath, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name)
        
        if (entry.isDirectory()) {
          await calculateSize(fullPath)
        } else {
          const stats = await fs.stat(fullPath)
          totalSize += stats.size
        }
      }
    }
    
    await calculateSize(pluginPath)
    return totalSize
  }
  
  /**
   * 清理资源
   */
  dispose(): void {
    // 关闭所有文件监听器
    for (const [pluginPath, watcher] of this.fileWatchers) {
      try {
        watcher.close()
      } catch (error) {
        console.warn(`关闭文件监听器失败 ${pluginPath}:`, error)
      }
    }
    
    this.fileWatchers.clear()
    this.moduleCache = {}
  }
}

export default PluginLoader
