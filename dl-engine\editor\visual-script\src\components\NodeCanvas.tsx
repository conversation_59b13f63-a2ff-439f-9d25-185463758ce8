/**
 * 节点画布组件
 * 
 * 渲染节点和连接的主画布
 */

import React, { forwardRef, useCallback, useRef, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import NodeRender<PERSON> from './NodeRenderer'
import ConnectionRenderer from './ConnectionRenderer'
import GridBackground from './GridBackground'
import { 
  ScriptGraph, 
  NodeInstance, 
  Connection,
  NodeEditorState,
  ExecutionState,
  Vector2
} from '../types'

/**
 * 节点画布属性
 */
export interface NodeCanvasProps {
  /** 脚本图形 */
  graph: ScriptGraph
  /** 编辑器状态 */
  editorState: NodeEditorState
  /** 选中的节点 */
  selectedNodes: string[]
  /** 选中的连接 */
  selectedConnections: string[]
  /** 执行状态 */
  executionState: ExecutionState
  /** 是否只读 */
  readonly: boolean
  /** 点击回调 */
  onClick?: (event: React.MouseEvent) => void
  /** 节点拖拽回调 */
  onNodeDrag?: (nodeId: string, delta: Vector2) => void
  /** 连接开始回调 */
  onConnectionStart?: (nodeId: string, socketId: string, socketType: 'input' | 'output') => void
  /** 连接结束回调 */
  onConnectionEnd?: (nodeId: string, socketId: string, socketType: 'input' | 'output') => void
  /** 缩放回调 */
  onZoom?: (delta: number, center?: Vector2) => void
  /** 平移回调 */
  onPan?: (delta: Vector2) => void
  /** 自定义类名 */
  className?: string
}

/**
 * 节点画布组件
 */
const NodeCanvas = forwardRef<HTMLDivElement, NodeCanvasProps>(({
  graph,
  editorState,
  selectedNodes,
  selectedConnections,
  executionState,
  readonly,
  onClick,
  onNodeDrag,
  onConnectionStart,
  onConnectionEnd,
  onZoom,
  onPan,
  className = ''
}, ref) => {
  const { t } = useTranslation()
  const isDragging = useRef(false)
  const lastMousePos = useRef<Vector2>({ x: 0, y: 0 })
  const dragStartPos = useRef<Vector2>({ x: 0, y: 0 })
  
  /**
   * 处理鼠标按下
   */
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    if (event.button === 1 || (event.button === 0 && event.ctrlKey)) {
      // 中键或Ctrl+左键开始平移
      isDragging.current = true
      lastMousePos.current = { x: event.clientX, y: event.clientY }
      dragStartPos.current = { x: event.clientX, y: event.clientY }
      event.preventDefault()
    }
  }, [])
  
  /**
   * 处理鼠标移动
   */
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (isDragging.current) {
      const delta = {
        x: event.clientX - lastMousePos.current.x,
        y: event.clientY - lastMousePos.current.y
      }
      
      onPan?.(delta)
      lastMousePos.current = { x: event.clientX, y: event.clientY }
    }
  }, [onPan])
  
  /**
   * 处理鼠标抬起
   */
  const handleMouseUp = useCallback((event: React.MouseEvent) => {
    if (isDragging.current) {
      isDragging.current = false
      
      // 如果移动距离很小，视为点击
      const distance = Math.sqrt(
        Math.pow(event.clientX - dragStartPos.current.x, 2) +
        Math.pow(event.clientY - dragStartPos.current.y, 2)
      )
      
      if (distance < 5) {
        onClick?.(event)
      }
    }
  }, [onClick])
  
  /**
   * 处理滚轮缩放
   */
  const handleWheel = useCallback((event: React.WheelEvent) => {
    event.preventDefault()
    
    const delta = -event.deltaY * 0.001
    const rect = (ref as React.RefObject<HTMLDivElement>).current?.getBoundingClientRect()
    
    if (rect) {
      const center = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      }
      onZoom?.(delta, center)
    }
  }, [onZoom, ref])
  
  /**
   * 处理上下文菜单
   */
  const handleContextMenu = useCallback((event: React.MouseEvent) => {
    event.preventDefault()
    // TODO: 显示上下文菜单
  }, [])
  
  /**
   * 计算变换样式
   */
  const getTransformStyle = () => {
    return {
      transform: `translate(${editorState.pan.x}px, ${editorState.pan.y}px) scale(${editorState.zoom})`,
      transformOrigin: '0 0'
    }
  }
  
  return (
    <div
      ref={ref}
      className={`node-canvas ${className}`}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onWheel={handleWheel}
      onContextMenu={handleContextMenu}
      style={{
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        position: 'relative',
        cursor: isDragging.current ? 'grabbing' : 'grab'
      }}
    >
      {/* 网格背景 */}
      <GridBackground
        zoom={editorState.zoom}
        pan={editorState.pan}
        gridSize={20}
        className="absolute inset-0"
      />
      
      {/* 节点和连接容器 */}
      <div
        className="node-canvas-content absolute inset-0"
        style={getTransformStyle()}
      >
        {/* 连接层 */}
        <svg
          className="absolute inset-0 pointer-events-none"
          style={{
            width: '100%',
            height: '100%',
            overflow: 'visible'
          }}
        >
          {graph.connections.map(connection => (
            <ConnectionRenderer
              key={connection.id}
              connection={connection}
              graph={graph}
              selected={selectedConnections.includes(connection.id)}
              executing={executionState === ExecutionState.RUNNING}
              readonly={readonly}
            />
          ))}
          
          {/* 正在创建的连接 */}
          {editorState.isConnecting && editorState.connectionStart && (
            <ConnectionRenderer
              connection={{
                id: 'temp',
                sourceNodeId: editorState.connectionStart.nodeId,
                sourceSocketId: editorState.connectionStart.socketId,
                targetNodeId: '',
                targetSocketId: '',
                temporary: true
              }}
              graph={graph}
              selected={false}
              executing={false}
              readonly={readonly}
            />
          )}
        </svg>
        
        {/* 节点层 */}
        <div className="relative">
          {graph.nodes.map(node => (
            <NodeRenderer
              key={node.id}
              node={node}
              selected={selectedNodes.includes(node.id)}
              executing={executionState === ExecutionState.RUNNING}
              readonly={readonly}
              onDrag={(delta) => onNodeDrag?.(node.id, delta)}
              onConnectionStart={(socketId, socketType) => 
                onConnectionStart?.(node.id, socketId, socketType)
              }
              onConnectionEnd={(socketId, socketType) => 
                onConnectionEnd?.(node.id, socketId, socketType)
              }
            />
          ))}
        </div>
      </div>
      
      {/* 选择框 */}
      {/* TODO: 实现选择框功能 */}
    </div>
  )
})

NodeCanvas.displayName = 'NodeCanvas'

export default NodeCanvas
