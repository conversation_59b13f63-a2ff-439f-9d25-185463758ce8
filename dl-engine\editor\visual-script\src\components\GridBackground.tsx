/**
 * 网格背景组件
 * 
 * 为节点编辑器提供网格背景
 */

import React from 'react'
import { Vector2 } from '../types'

/**
 * 网格背景属性
 */
export interface GridBackgroundProps {
  /** 缩放级别 */
  zoom: number
  /** 平移偏移 */
  pan: Vector2
  /** 网格大小 */
  gridSize: number
  /** 网格颜色 */
  gridColor?: string
  /** 主网格颜色 */
  majorGridColor?: string
  /** 主网格间隔 */
  majorGridInterval?: number
  /** 自定义类名 */
  className?: string
}

/**
 * 网格背景组件
 */
const GridBackground: React.FC<GridBackgroundProps> = ({
  zoom,
  pan,
  gridSize,
  gridColor = '#e0e0e0',
  majorGridColor = '#c0c0c0',
  majorGridInterval = 5,
  className = ''
}) => {
  /**
   * 计算网格参数
   */
  const scaledGridSize = gridSize * zoom
  const offsetX = pan.x % scaledGridSize
  const offsetY = pan.y % scaledGridSize
  
  /**
   * 生成网格线
   */
  const generateGridLines = () => {
    const lines: JSX.Element[] = []
    const viewportWidth = 2000 // 假设视口宽度
    const viewportHeight = 2000 // 假设视口高度
    
    // 计算需要绘制的网格线数量
    const horizontalLines = Math.ceil(viewportHeight / scaledGridSize) + 2
    const verticalLines = Math.ceil(viewportWidth / scaledGridSize) + 2
    
    // 绘制垂直线
    for (let i = 0; i < verticalLines; i++) {
      const x = offsetX + i * scaledGridSize
      const isMajor = i % majorGridInterval === 0
      
      lines.push(
        <line
          key={`v-${i}`}
          x1={x}
          y1={0}
          x2={x}
          y2={viewportHeight}
          stroke={isMajor ? majorGridColor : gridColor}
          strokeWidth={isMajor ? 1 : 0.5}
        />
      )
    }
    
    // 绘制水平线
    for (let i = 0; i < horizontalLines; i++) {
      const y = offsetY + i * scaledGridSize
      const isMajor = i % majorGridInterval === 0
      
      lines.push(
        <line
          key={`h-${i}`}
          x1={0}
          y1={y}
          x2={viewportWidth}
          y2={y}
          stroke={isMajor ? majorGridColor : gridColor}
          strokeWidth={isMajor ? 1 : 0.5}
        />
      )
    }
    
    return lines
  }
  
  /**
   * 生成网格点（当缩放很大时）
   */
  const generateGridDots = () => {
    if (scaledGridSize < 10) return null
    
    const dots: JSX.Element[] = []
    const viewportWidth = 2000
    const viewportHeight = 2000
    
    const horizontalDots = Math.ceil(viewportHeight / scaledGridSize) + 2
    const verticalDots = Math.ceil(viewportWidth / scaledGridSize) + 2
    
    for (let i = 0; i < verticalDots; i++) {
      for (let j = 0; j < horizontalDots; j++) {
        const x = offsetX + i * scaledGridSize
        const y = offsetY + j * scaledGridSize
        const isMajor = (i % majorGridInterval === 0) && (j % majorGridInterval === 0)
        
        dots.push(
          <circle
            key={`dot-${i}-${j}`}
            cx={x}
            cy={y}
            r={isMajor ? 1.5 : 1}
            fill={isMajor ? majorGridColor : gridColor}
          />
        )
      }
    }
    
    return dots
  }
  
  /**
   * 决定使用线条还是点
   */
  const useDotsInsteadOfLines = scaledGridSize > 50
  
  return (
    <svg
      className={`grid-background ${className}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: -1
      }}
    >
      {/* 背景 */}
      <rect
        width="100%"
        height="100%"
        fill="#fafafa"
      />
      
      {/* 网格 */}
      {useDotsInsteadOfLines ? generateGridDots() : generateGridLines()}
      
      {/* 原点指示器 */}
      {zoom > 0.5 && (
        <g>
          <line
            x1={pan.x - 10}
            y1={pan.y}
            x2={pan.x + 10}
            y2={pan.y}
            stroke="#ff4757"
            strokeWidth="2"
          />
          <line
            x1={pan.x}
            y1={pan.y - 10}
            x2={pan.x}
            y2={pan.y + 10}
            stroke="#ff4757"
            strokeWidth="2"
          />
        </g>
      )}
    </svg>
  )
}

export default GridBackground
