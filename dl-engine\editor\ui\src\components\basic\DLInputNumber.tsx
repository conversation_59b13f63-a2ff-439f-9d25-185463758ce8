/**
 * DL-Engine 数字输入框组件
 * 
 * 基于 Ant Design InputNumber 的增强版本
 */

import React from 'react'
import { InputNumber, InputNumberProps } from 'antd'
import classNames from 'classnames'

/**
 * DL数字输入框属性
 */
export interface DLInputNumberProps extends InputNumberProps {
  /** 输入框标签 */
  label?: string
  /** 单位 */
  unit?: string
  /** 验证状态 */
  status?: 'error' | 'warning' | 'success'
  /** 验证消息 */
  message?: string
  /** 自定义类名 */
  className?: string
}

/**
 * DL数字输入框组件
 */
const DLInputNumber: React.FC<DLInputNumberProps> = ({
  label,
  unit,
  status,
  message,
  className,
  ...props
}) => {
  /**
   * 输入框类名
   */
  const inputClassName = classNames(
    'dl-input-number',
    {
      'dl-input-number--with-label': label,
      'dl-input-number--with-unit': unit,
      'dl-input-number--error': status === 'error',
      'dl-input-number--warning': status === 'warning',
      'dl-input-number--success': status === 'success'
    },
    className
  )
  
  return (
    <div className={inputClassName}>
      {/* 标签 */}
      {label && (
        <div className="dl-input-number-label mb-1">
          <span className="text-sm text-gray-700">{label}</span>
        </div>
      )}
      
      {/* 输入框容器 */}
      <div className="dl-input-number-wrapper flex items-center">
        <InputNumber
          {...props}
          status={status}
          style={{ width: '100%', ...props.style }}
        />
        
        {/* 单位 */}
        {unit && (
          <span className="ml-2 text-sm text-gray-500">{unit}</span>
        )}
      </div>
      
      {/* 验证消息 */}
      {message && (
        <div className={`dl-input-number-message mt-1 text-xs ${
          status === 'error' ? 'text-red-500' :
          status === 'warning' ? 'text-yellow-500' :
          status === 'success' ? 'text-green-500' :
          'text-gray-500'
        }`}>
          {message}
        </div>
      )}
    </div>
  )
}

export default DLInputNumber
