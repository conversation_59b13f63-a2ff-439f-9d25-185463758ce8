/**
 * 插件系统工具函数
 * 
 * 提供插件开发和管理中常用的工具函数
 */

import { PluginMetadata, PluginPermission, PluginType } from './PluginAPI'

/**
 * 验证插件ID格式
 */
export function validatePluginId(id: string): boolean {
  return /^[a-z0-9-_.]+$/.test(id)
}

/**
 * 验证版本号格式
 */
export function validateVersion(version: string): boolean {
  return /^\d+\.\d+\.\d+(-[a-zA-Z0-9-]+)?(\+[a-zA-Z0-9-]+)?$/.test(version)
}

/**
 * 比较版本号
 */
export function compareVersions(version1: string, version2: string): number {
  const v1Parts = version1.split('.').map(Number)
  const v2Parts = version2.split('.').map(Number)
  
  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const v1Part = v1Parts[i] || 0
    const v2Part = v2Parts[i] || 0
    
    if (v1Part > v2Part) return 1
    if (v1Part < v2Part) return -1
  }
  
  return 0
}

/**
 * 检查版本是否满足要求
 */
export function satisfiesVersion(version: string, requirement: string): boolean {
  if (requirement === '*') return true
  
  // 简化的版本匹配，实际应该支持更复杂的语义化版本规则
  if (requirement.startsWith('^')) {
    const requiredVersion = requirement.slice(1)
    return compareVersions(version, requiredVersion) >= 0
  }
  
  if (requirement.startsWith('~')) {
    const requiredVersion = requirement.slice(1)
    const vParts = version.split('.').map(Number)
    const rParts = requiredVersion.split('.').map(Number)
    
    return vParts[0] === rParts[0] && vParts[1] === rParts[1] && vParts[2] >= rParts[2]
  }
  
  return version === requirement
}

/**
 * 生成插件ID
 */
export function generatePluginId(name: string, author?: string): string {
  const cleanName = name.toLowerCase()
    .replace(/[^a-z0-9-_.]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
  
  if (author) {
    const cleanAuthor = author.toLowerCase()
      .replace(/[^a-z0-9-_.]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
    
    return `${cleanAuthor}.${cleanName}`
  }
  
  return cleanName
}

/**
 * 验证插件元数据
 */
export function validatePluginMetadata(metadata: any): string[] {
  const errors: string[] = []
  
  // 必需字段检查
  const requiredFields = ['id', 'name', 'version', 'type', 'author']
  for (const field of requiredFields) {
    if (!metadata[field]) {
      errors.push(`缺少必需字段: ${field}`)
    }
  }
  
  // ID格式检查
  if (metadata.id && !validatePluginId(metadata.id)) {
    errors.push('插件ID格式无效')
  }
  
  // 版本格式检查
  if (metadata.version && !validateVersion(metadata.version)) {
    errors.push('版本号格式无效')
  }
  
  // 类型检查
  if (metadata.type && !Object.values(PluginType).includes(metadata.type)) {
    errors.push('插件类型无效')
  }
  
  // 权限检查
  if (metadata.permissions) {
    if (!Array.isArray(metadata.permissions)) {
      errors.push('权限列表必须是数组')
    } else {
      for (const permission of metadata.permissions) {
        if (!Object.values(PluginPermission).includes(permission)) {
          errors.push(`无效的权限: ${permission}`)
        }
      }
    }
  }
  
  // 依赖检查
  if (metadata.dependencies) {
    if (typeof metadata.dependencies !== 'object') {
      errors.push('依赖列表必须是对象')
    } else {
      for (const [depId, version] of Object.entries(metadata.dependencies)) {
        if (!validatePluginId(depId)) {
          errors.push(`无效的依赖ID: ${depId}`)
        }
        if (typeof version !== 'string') {
          errors.push(`依赖版本必须是字符串: ${depId}`)
        }
      }
    }
  }
  
  return errors
}

/**
 * 创建插件元数据模板
 */
export function createPluginMetadataTemplate(options: {
  name: string
  author: string
  type: PluginType
  description?: string
}): PluginMetadata {
  return {
    id: generatePluginId(options.name, options.author),
    name: options.name,
    version: '1.0.0',
    description: options.description || '',
    author: options.author,
    type: options.type,
    main: 'index.js',
    permissions: [],
    dependencies: {},
    keywords: []
  }
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`
}

/**
 * 格式化时间
 */
export function formatTime(timestamp: number): string {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) {
    return `${days}天前`
  } else if (hours > 0) {
    return `${hours}小时前`
  } else if (minutes > 0) {
    return `${minutes}分钟前`
  } else {
    return '刚刚'
  }
}

/**
 * 深度克隆对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as any
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as any
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as any
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }
  
  return obj
}

/**
 * 合并配置对象
 */
export function mergeConfig<T extends Record<string, any>>(
  defaultConfig: T,
  userConfig: Partial<T>
): T {
  const merged = deepClone(defaultConfig)
  
  for (const key in userConfig) {
    if (userConfig.hasOwnProperty(key)) {
      const userValue = userConfig[key]
      const defaultValue = merged[key]
      
      if (
        typeof userValue === 'object' &&
        userValue !== null &&
        !Array.isArray(userValue) &&
        typeof defaultValue === 'object' &&
        defaultValue !== null &&
        !Array.isArray(defaultValue)
      ) {
        merged[key] = mergeConfig(defaultValue, userValue)
      } else {
        merged[key] = userValue as any
      }
    }
  }
  
  return merged
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let lastTime = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    
    if (now - lastTime >= wait) {
      lastTime = now
      func(...args)
    }
  }
}

/**
 * 创建唯一ID
 */
export function createUniqueId(prefix = 'id'): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 检查对象是否为空
 */
export function isEmpty(obj: any): boolean {
  if (obj == null) return true
  if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0
  if (typeof obj === 'object') return Object.keys(obj).length === 0
  return false
}

/**
 * 安全的JSON解析
 */
export function safeJsonParse<T = any>(json: string, defaultValue: T): T {
  try {
    return JSON.parse(json)
  } catch {
    return defaultValue
  }
}

/**
 * 安全的JSON字符串化
 */
export function safeJsonStringify(obj: any, space?: number): string {
  try {
    return JSON.stringify(obj, null, space)
  } catch {
    return '{}'
  }
}

/**
 * 检查是否为有效的URL
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(filename: string): string {
  const lastDotIndex = filename.lastIndexOf('.')
  return lastDotIndex !== -1 ? filename.slice(lastDotIndex + 1).toLowerCase() : ''
}

/**
 * 检查文件类型
 */
export function isImageFile(filename: string): boolean {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
  return imageExtensions.includes(getFileExtension(filename))
}

/**
 * 检查是否为JavaScript文件
 */
export function isJavaScriptFile(filename: string): boolean {
  const jsExtensions = ['js', 'mjs', 'jsx']
  return jsExtensions.includes(getFileExtension(filename))
}

/**
 * 检查是否为TypeScript文件
 */
export function isTypeScriptFile(filename: string): boolean {
  const tsExtensions = ['ts', 'tsx']
  return tsExtensions.includes(getFileExtension(filename))
}

/**
 * 创建错误对象
 */
export function createError(message: string, code?: string, details?: any): Error {
  const error = new Error(message) as any
  if (code) error.code = code
  if (details) error.details = details
  return error
}

/**
 * 重试函数
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts = 3,
  delay = 1000
): Promise<T> {
  let lastError: Error
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      
      if (attempt < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, delay * attempt))
      }
    }
  }
  
  throw lastError!
}
