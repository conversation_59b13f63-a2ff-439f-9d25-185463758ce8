/**
 * 插件管理器
 * 
 * 负责插件的加载、卸载、生命周期管理
 */

import { EventEmitter } from 'events'
import { 
  Plugin, 
  PluginMetadata, 
  PluginConfig, 
  PluginStatus, 
  PluginContext,
  IPluginAPI,
  PluginLogger
} from './PluginAPI'
import PluginLoader from './PluginLoader'
import PluginRegistry from './PluginRegistry'

/**
 * 插件实例信息
 */
interface PluginInstance {
  metadata: PluginMetadata
  config: PluginConfig
  status: PluginStatus
  instance: Plugin | null
  context: PluginContext
  error?: Error
  loadTime?: number
  activateTime?: number
}

/**
 * 插件管理器事件
 */
export interface PluginManagerEvents {
  'plugin-loaded': (pluginId: string, metadata: PluginMetadata) => void
  'plugin-unloaded': (pluginId: string) => void
  'plugin-activated': (pluginId: string) => void
  'plugin-deactivated': (pluginId: string) => void
  'plugin-error': (pluginId: string, error: Error) => void
  'plugin-config-changed': (pluginId: string, config: PluginConfig) => void
}

/**
 * 插件管理器配置
 */
export interface PluginManagerConfig {
  /** 插件目录 */
  pluginDir: string
  /** 是否启用开发模式 */
  devMode: boolean
  /** 是否启用热重载 */
  hotReload: boolean
  /** 插件超时时间（毫秒） */
  timeout: number
  /** 最大并发加载数 */
  maxConcurrentLoads: number
}

/**
 * 插件管理器类
 */
export class PluginManager extends EventEmitter {
  private config: PluginManagerConfig
  private plugins: Map<string, PluginInstance> = new Map()
  private loader: PluginLoader
  private registry: PluginRegistry
  private api: IPluginAPI
  private isInitialized = false
  
  constructor(config: PluginManagerConfig, api: IPluginAPI) {
    super()
    this.config = config
    this.api = api
    this.loader = new PluginLoader(config)
    this.registry = new PluginRegistry()
  }
  
  /**
   * 初始化插件管理器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      throw new Error('插件管理器已经初始化')
    }
    
    try {
      // 初始化插件注册表
      await this.registry.initialize()
      
      // 扫描插件目录
      await this.scanPlugins()
      
      // 加载已启用的插件
      await this.loadEnabledPlugins()
      
      this.isInitialized = true
      
    } catch (error) {
      throw new Error(`插件管理器初始化失败: ${error.message}`)
    }
  }
  
  /**
   * 扫描插件目录
   */
  async scanPlugins(): Promise<void> {
    const pluginPaths = await this.loader.scanPluginDirectory()
    
    for (const pluginPath of pluginPaths) {
      try {
        const metadata = await this.loader.loadPluginMetadata(pluginPath)
        await this.registry.register(metadata, pluginPath)
      } catch (error) {
        console.warn(`扫描插件失败 ${pluginPath}:`, error)
      }
    }
  }
  
  /**
   * 加载已启用的插件
   */
  async loadEnabledPlugins(): Promise<void> {
    const enabledPlugins = await this.registry.getEnabledPlugins()
    
    // 按依赖顺序排序
    const sortedPlugins = this.sortPluginsByDependencies(enabledPlugins)
    
    // 并发加载插件（受最大并发数限制）
    const loadPromises: Promise<void>[] = []
    let loadingCount = 0
    
    for (const metadata of sortedPlugins) {
      if (loadingCount >= this.config.maxConcurrentLoads) {
        await Promise.race(loadPromises)
        loadingCount--
      }
      
      const loadPromise = this.loadPlugin(metadata.id).finally(() => {
        loadingCount--
      })
      
      loadPromises.push(loadPromise)
      loadingCount++
    }
    
    await Promise.all(loadPromises)
  }
  
  /**
   * 按依赖关系排序插件
   */
  private sortPluginsByDependencies(plugins: PluginMetadata[]): PluginMetadata[] {
    const sorted: PluginMetadata[] = []
    const visited = new Set<string>()
    const visiting = new Set<string>()
    
    const visit = (plugin: PluginMetadata) => {
      if (visiting.has(plugin.id)) {
        throw new Error(`检测到循环依赖: ${plugin.id}`)
      }
      
      if (visited.has(plugin.id)) {
        return
      }
      
      visiting.add(plugin.id)
      
      // 先访问依赖
      if (plugin.dependencies) {
        for (const depId of Object.keys(plugin.dependencies)) {
          const depPlugin = plugins.find(p => p.id === depId)
          if (depPlugin) {
            visit(depPlugin)
          }
        }
      }
      
      visiting.delete(plugin.id)
      visited.add(plugin.id)
      sorted.push(plugin)
    }
    
    for (const plugin of plugins) {
      visit(plugin)
    }
    
    return sorted
  }
  
  /**
   * 加载插件
   */
  async loadPlugin(pluginId: string): Promise<void> {
    if (this.plugins.has(pluginId)) {
      throw new Error(`插件 ${pluginId} 已经加载`)
    }
    
    try {
      const startTime = Date.now()
      
      // 获取插件元数据和路径
      const registryEntry = await this.registry.get(pluginId)
      if (!registryEntry) {
        throw new Error(`插件 ${pluginId} 未注册`)
      }
      
      const { metadata, path } = registryEntry
      
      // 获取插件配置
      const config = await this.registry.getPluginConfig(pluginId)
      
      // 创建插件上下文
      const context = this.createPluginContext(metadata, config, path)
      
      // 创建插件实例
      const pluginInstance: PluginInstance = {
        metadata,
        config,
        status: PluginStatus.LOADING,
        instance: null,
        context,
        loadTime: Date.now() - startTime
      }
      
      this.plugins.set(pluginId, pluginInstance)
      
      // 加载插件代码
      const PluginClass = await this.loader.loadPluginCode(path)
      
      // 实例化插件
      const instance = new PluginClass(context, this.api)
      pluginInstance.instance = instance
      
      // 更新状态
      pluginInstance.status = PluginStatus.INACTIVE
      
      this.emit('plugin-loaded', pluginId, metadata)
      
      // 如果配置为启用，则激活插件
      if (config.enabled) {
        await this.activatePlugin(pluginId)
      }
      
    } catch (error) {
      const pluginInstance = this.plugins.get(pluginId)
      if (pluginInstance) {
        pluginInstance.status = PluginStatus.ERROR
        pluginInstance.error = error
      }
      
      this.emit('plugin-error', pluginId, error)
      throw error
    }
  }
  
  /**
   * 卸载插件
   */
  async unloadPlugin(pluginId: string): Promise<void> {
    const pluginInstance = this.plugins.get(pluginId)
    if (!pluginInstance) {
      throw new Error(`插件 ${pluginId} 未加载`)
    }
    
    try {
      // 如果插件处于激活状态，先停用
      if (pluginInstance.status === PluginStatus.ACTIVE) {
        await this.deactivatePlugin(pluginId)
      }
      
      // 清理插件实例
      if (pluginInstance.instance) {
        // 调用插件的清理方法（如果有）
        if (typeof pluginInstance.instance.dispose === 'function') {
          await pluginInstance.instance.dispose()
        }
      }
      
      // 从管理器中移除
      this.plugins.delete(pluginId)
      
      this.emit('plugin-unloaded', pluginId)
      
    } catch (error) {
      this.emit('plugin-error', pluginId, error)
      throw error
    }
  }
  
  /**
   * 激活插件
   */
  async activatePlugin(pluginId: string): Promise<void> {
    const pluginInstance = this.plugins.get(pluginId)
    if (!pluginInstance) {
      throw new Error(`插件 ${pluginId} 未加载`)
    }
    
    if (pluginInstance.status === PluginStatus.ACTIVE) {
      return // 已经激活
    }
    
    if (!pluginInstance.instance) {
      throw new Error(`插件 ${pluginId} 实例不存在`)
    }
    
    try {
      const startTime = Date.now()
      
      // 检查依赖是否已激活
      await this.checkDependencies(pluginInstance.metadata)
      
      // 调用插件的激活方法
      await Promise.race([
        pluginInstance.instance.activate(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('插件激活超时')), this.config.timeout)
        )
      ])
      
      // 更新状态
      pluginInstance.status = PluginStatus.ACTIVE
      pluginInstance.activateTime = Date.now() - startTime
      
      this.emit('plugin-activated', pluginId)
      
    } catch (error) {
      pluginInstance.status = PluginStatus.ERROR
      pluginInstance.error = error
      
      this.emit('plugin-error', pluginId, error)
      throw error
    }
  }
  
  /**
   * 停用插件
   */
  async deactivatePlugin(pluginId: string): Promise<void> {
    const pluginInstance = this.plugins.get(pluginId)
    if (!pluginInstance) {
      throw new Error(`插件 ${pluginId} 未加载`)
    }
    
    if (pluginInstance.status !== PluginStatus.ACTIVE) {
      return // 已经停用
    }
    
    if (!pluginInstance.instance) {
      throw new Error(`插件 ${pluginId} 实例不存在`)
    }
    
    try {
      // 调用插件的停用方法
      await Promise.race([
        pluginInstance.instance.deactivate(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('插件停用超时')), this.config.timeout)
        )
      ])
      
      // 更新状态
      pluginInstance.status = PluginStatus.INACTIVE
      
      this.emit('plugin-deactivated', pluginId)
      
    } catch (error) {
      pluginInstance.status = PluginStatus.ERROR
      pluginInstance.error = error
      
      this.emit('plugin-error', pluginId, error)
      throw error
    }
  }
  
  /**
   * 检查插件依赖
   */
  private async checkDependencies(metadata: PluginMetadata): Promise<void> {
    if (!metadata.dependencies) return
    
    for (const [depId, version] of Object.entries(metadata.dependencies)) {
      const depInstance = this.plugins.get(depId)
      
      if (!depInstance) {
        throw new Error(`依赖插件 ${depId} 未加载`)
      }
      
      if (depInstance.status !== PluginStatus.ACTIVE) {
        throw new Error(`依赖插件 ${depId} 未激活`)
      }
      
      // TODO: 检查版本兼容性
    }
  }
  
  /**
   * 创建插件上下文
   */
  private createPluginContext(
    metadata: PluginMetadata, 
    config: PluginConfig, 
    rootPath: string
  ): PluginContext {
    const logger: PluginLogger = {
      debug: (message, ...args) => console.debug(`[${metadata.id}]`, message, ...args),
      info: (message, ...args) => console.info(`[${metadata.id}]`, message, ...args),
      warn: (message, ...args) => console.warn(`[${metadata.id}]`, message, ...args),
      error: (message, ...args) => console.error(`[${metadata.id}]`, message, ...args)
    }
    
    return {
      metadata,
      config,
      rootPath,
      assetsPath: `${rootPath}/assets`,
      logger,
      events: new EventEmitter()
    }
  }
  
  /**
   * 获取插件信息
   */
  getPlugin(pluginId: string): PluginInstance | undefined {
    return this.plugins.get(pluginId)
  }
  
  /**
   * 获取所有插件
   */
  getAllPlugins(): PluginInstance[] {
    return Array.from(this.plugins.values())
  }
  
  /**
   * 获取激活的插件
   */
  getActivePlugins(): PluginInstance[] {
    return this.getAllPlugins().filter(p => p.status === PluginStatus.ACTIVE)
  }
  
  /**
   * 更新插件配置
   */
  async updatePluginConfig(pluginId: string, config: Partial<PluginConfig>): Promise<void> {
    const pluginInstance = this.plugins.get(pluginId)
    if (!pluginInstance) {
      throw new Error(`插件 ${pluginId} 未加载`)
    }
    
    // 合并配置
    const newConfig = { ...pluginInstance.config, ...config }
    
    // 保存到注册表
    await this.registry.setPluginConfig(pluginId, newConfig)
    
    // 更新实例配置
    pluginInstance.config = newConfig
    pluginInstance.context.config = newConfig
    
    this.emit('plugin-config-changed', pluginId, newConfig)
  }
  
  /**
   * 重新加载插件
   */
  async reloadPlugin(pluginId: string): Promise<void> {
    await this.unloadPlugin(pluginId)
    await this.loadPlugin(pluginId)
  }
  
  /**
   * 销毁插件管理器
   */
  async dispose(): Promise<void> {
    // 停用所有插件
    const activePlugins = this.getActivePlugins()
    for (const plugin of activePlugins) {
      try {
        await this.deactivatePlugin(plugin.metadata.id)
      } catch (error) {
        console.error(`停用插件 ${plugin.metadata.id} 失败:`, error)
      }
    }
    
    // 卸载所有插件
    const allPlugins = this.getAllPlugins()
    for (const plugin of allPlugins) {
      try {
        await this.unloadPlugin(plugin.metadata.id)
      } catch (error) {
        console.error(`卸载插件 ${plugin.metadata.id} 失败:`, error)
      }
    }
    
    // 清理资源
    this.plugins.clear()
    this.removeAllListeners()
    this.isInitialized = false
  }
}

export default PluginManager
