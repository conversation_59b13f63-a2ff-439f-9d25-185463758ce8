/**
 * XR工具函数
 * 
 * 提供XR开发中常用的工具函数
 */

import * as THREE from 'three'

/**
 * 检查WebXR是否可用
 */
export function isWebXRAvailable(): boolean {
  return 'xr' in navigator
}

/**
 * 检查特定XR会话类型是否支持
 */
export async function isXRSessionSupported(sessionType: XRSessionMode): Promise<boolean> {
  if (!isWebXRAvailable()) {
    return false
  }
  
  try {
    return await navigator.xr!.isSessionSupported(sessionType)
  } catch (error) {
    console.warn('检查XR会话支持时出错:', error)
    return false
  }
}

/**
 * 将XR姿态转换为THREE.js对象
 */
export function xrPoseToThreeJS(pose: XRPose): {
  position: THREE.Vector3
  rotation: THREE.Quaternion
} {
  return {
    position: new THREE.Vector3(
      pose.transform.position.x,
      pose.transform.position.y,
      pose.transform.position.z
    ),
    rotation: new THREE.Quaternion(
      pose.transform.orientation.x,
      pose.transform.orientation.y,
      pose.transform.orientation.z,
      pose.transform.orientation.w
    )
  }
}

/**
 * 将THREE.js位置和旋转转换为XR变换
 */
export function threeJSToXRTransform(position: THREE.Vector3, rotation: THREE.Quaternion): XRRigidTransform {
  return new XRRigidTransform(
    {
      x: position.x,
      y: position.y,
      z: position.z,
      w: 1
    },
    {
      x: rotation.x,
      y: rotation.y,
      z: rotation.z,
      w: rotation.w
    }
  )
}

/**
 * 计算两个3D点之间的距离
 */
export function distance3D(a: THREE.Vector3, b: THREE.Vector3): number {
  return a.distanceTo(b)
}

/**
 * 计算射线与平面的交点
 */
export function rayPlaneIntersection(
  rayOrigin: THREE.Vector3,
  rayDirection: THREE.Vector3,
  planePoint: THREE.Vector3,
  planeNormal: THREE.Vector3
): THREE.Vector3 | null {
  const ray = new THREE.Ray(rayOrigin, rayDirection)
  const plane = new THREE.Plane(planeNormal, -planeNormal.dot(planePoint))
  const intersection = new THREE.Vector3()
  
  if (ray.intersectPlane(plane, intersection)) {
    return intersection
  }
  
  return null
}

/**
 * 将世界坐标转换为屏幕坐标
 */
export function worldToScreen(
  worldPosition: THREE.Vector3,
  camera: THREE.Camera,
  renderer: THREE.WebGLRenderer
): THREE.Vector2 {
  const vector = worldPosition.clone()
  vector.project(camera)
  
  const size = renderer.getSize(new THREE.Vector2())
  
  return new THREE.Vector2(
    (vector.x + 1) * size.x / 2,
    (-vector.y + 1) * size.y / 2
  )
}

/**
 * 将屏幕坐标转换为世界坐标
 */
export function screenToWorld(
  screenPosition: THREE.Vector2,
  camera: THREE.Camera,
  renderer: THREE.WebGLRenderer,
  depth: number = 1
): THREE.Vector3 {
  const size = renderer.getSize(new THREE.Vector2())
  
  const normalizedPosition = new THREE.Vector2(
    (screenPosition.x / size.x) * 2 - 1,
    -(screenPosition.y / size.y) * 2 + 1
  )
  
  const vector = new THREE.Vector3(normalizedPosition.x, normalizedPosition.y, depth)
  vector.unproject(camera)
  
  return vector
}

/**
 * 创建控制器射线可视化
 */
export function createControllerRay(length: number = 5, color: number = 0x00ff00): THREE.Line {
  const geometry = new THREE.BufferGeometry().setFromPoints([
    new THREE.Vector3(0, 0, 0),
    new THREE.Vector3(0, 0, -length)
  ])
  
  const material = new THREE.LineBasicMaterial({ color })
  
  return new THREE.Line(geometry, material)
}

/**
 * 创建控制器模型
 */
export function createControllerModel(color: number = 0x333333): THREE.Group {
  const group = new THREE.Group()
  
  // 控制器主体
  const bodyGeometry = new THREE.BoxGeometry(0.05, 0.05, 0.15)
  const bodyMaterial = new THREE.MeshBasicMaterial({ color })
  const body = new THREE.Mesh(bodyGeometry, bodyMaterial)
  group.add(body)
  
  // 触发器
  const triggerGeometry = new THREE.BoxGeometry(0.02, 0.02, 0.05)
  const triggerMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 })
  const trigger = new THREE.Mesh(triggerGeometry, triggerMaterial)
  trigger.position.set(0, -0.02, 0.05)
  group.add(trigger)
  
  return group
}

/**
 * 创建手部模型
 */
export function createHandModel(): THREE.Group {
  const group = new THREE.Group()
  
  // 手掌
  const palmGeometry = new THREE.BoxGeometry(0.08, 0.02, 0.1)
  const palmMaterial = new THREE.MeshBasicMaterial({ color: 0xffdbac })
  const palm = new THREE.Mesh(palmGeometry, palmMaterial)
  group.add(palm)
  
  // 手指（简化版本）
  const fingerGeometry = new THREE.CylinderGeometry(0.008, 0.008, 0.06)
  const fingerMaterial = new THREE.MeshBasicMaterial({ color: 0xffdbac })
  
  for (let i = 0; i < 5; i++) {
    const finger = new THREE.Mesh(fingerGeometry, fingerMaterial)
    finger.position.set((i - 2) * 0.02, 0.01, 0.04)
    finger.rotation.x = Math.PI / 2
    group.add(finger)
  }
  
  return group
}

/**
 * 计算手势识别
 */
export function recognizeGesture(handJoints: { [jointName: string]: THREE.Vector3 }): string {
  // 简化的手势识别
  // 实际应用中需要更复杂的算法
  
  if (!handJoints['index_finger_tip'] || !handJoints['thumb_tip']) {
    return 'unknown'
  }
  
  const indexTip = handJoints['index_finger_tip']
  const thumbTip = handJoints['thumb_tip']
  const distance = indexTip.distanceTo(thumbTip)
  
  if (distance < 0.03) {
    return 'pinch'
  }
  
  return 'open'
}

/**
 * 创建3D文本
 */
export function create3DText(
  text: string,
  font: THREE.Font,
  size: number = 0.1,
  color: number = 0x000000
): THREE.Mesh {
  const textGeometry = new THREE.TextGeometry(text, {
    font,
    size,
    height: 0.01,
    curveSegments: 12,
    bevelEnabled: false
  })
  
  textGeometry.computeBoundingBox()
  const centerOffsetX = -0.5 * (textGeometry.boundingBox!.max.x - textGeometry.boundingBox!.min.x)
  const centerOffsetY = -0.5 * (textGeometry.boundingBox!.max.y - textGeometry.boundingBox!.min.y)
  
  textGeometry.translate(centerOffsetX, centerOffsetY, 0)
  
  const textMaterial = new THREE.MeshBasicMaterial({ color })
  
  return new THREE.Mesh(textGeometry, textMaterial)
}

/**
 * 创建3D按钮
 */
export function create3DButton(
  width: number = 0.2,
  height: number = 0.1,
  depth: number = 0.02,
  color: number = 0x4CAF50
): THREE.Group {
  const group = new THREE.Group()
  
  // 按钮主体
  const buttonGeometry = new THREE.BoxGeometry(width, height, depth)
  const buttonMaterial = new THREE.MeshBasicMaterial({ color })
  const button = new THREE.Mesh(buttonGeometry, buttonMaterial)
  group.add(button)
  
  // 按钮边框
  const edgesGeometry = new THREE.EdgesGeometry(buttonGeometry)
  const edgesMaterial = new THREE.LineBasicMaterial({ color: 0x333333 })
  const edges = new THREE.LineSegments(edgesGeometry, edgesMaterial)
  group.add(edges)
  
  return group
}

/**
 * 平滑插值函数
 */
export function smoothStep(edge0: number, edge1: number, x: number): number {
  const t = Math.max(0, Math.min(1, (x - edge0) / (edge1 - edge0)))
  return t * t * (3 - 2 * t)
}

/**
 * 弹性插值函数
 */
export function easeOutElastic(t: number): number {
  const c4 = (2 * Math.PI) / 3
  
  return t === 0
    ? 0
    : t === 1
    ? 1
    : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1
}

/**
 * 检查点是否在3D边界框内
 */
export function isPointInBoundingBox(point: THREE.Vector3, box: THREE.Box3): boolean {
  return box.containsPoint(point)
}

/**
 * 获取对象的世界边界框
 */
export function getWorldBoundingBox(object: THREE.Object3D): THREE.Box3 {
  const box = new THREE.Box3()
  box.setFromObject(object)
  return box
}

/**
 * 创建网格地面
 */
export function createGridFloor(size: number = 10, divisions: number = 10): THREE.GridHelper {
  return new THREE.GridHelper(size, divisions, 0x888888, 0xcccccc)
}

/**
 * 创建天空盒
 */
export function createSkybox(textureUrls: string[]): THREE.Mesh {
  const loader = new THREE.CubeTextureLoader()
  const texture = loader.load(textureUrls)
  
  const geometry = new THREE.BoxGeometry(1000, 1000, 1000)
  const material = new THREE.MeshBasicMaterial({
    envMap: texture,
    side: THREE.BackSide
  })
  
  return new THREE.Mesh(geometry, material)
}
