/**
 * XR UI面板组件
 * 
 * 在VR/AR环境中显示的3D UI面板
 */

import React, { useRef, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import * as THREE from 'three'
import WebLayer3D from '../WebLayer3D'
import { XRInteractionSystem } from '../XRInteractionSystem'

/**
 * XR UI面板属性
 */
export interface XRUIPanelProps {
  /** 面板位置 */
  position?: [number, number, number]
  /** 面板旋转 */
  rotation?: [number, number, number]
  /** 面板尺寸 */
  size?: [number, number]
  /** 像素密度 */
  pixelDensity?: number
  /** 是否可交互 */
  interactive?: boolean
  /** 是否跟随用户 */
  followUser?: boolean
  /** 跟随距离 */
  followDistance?: number
  /** 面板内容 */
  children: React.ReactNode
  /** 面板类名 */
  className?: string
  /** 面板样式 */
  style?: React.CSSProperties
}

/**
 * XR UI面板组件
 */
const XRUIPanel: React.FC<XRUIPanelProps> = ({
  position = [0, 1.5, -2],
  rotation = [0, 0, 0],
  size = [1, 0.75],
  pixelDensity = 1,
  interactive = true,
  followUser = false,
  followDistance = 2,
  children,
  className = '',
  style = {}
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const webLayerRef = useRef<WebLayer3D | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  
  /**
   * 初始化Web层
   */
  useEffect(() => {
    if (!containerRef.current) return
    
    // 创建Web层
    const webLayer = new WebLayer3D(containerRef.current, {
      width: size[0],
      height: size[1],
      pixelDensity,
      interactive
    })
    
    // 设置位置和旋转
    webLayer.setPosition(position[0], position[1], position[2])
    webLayer.setRotation(rotation[0], rotation[1], rotation[2])
    
    webLayerRef.current = webLayer
    setIsInitialized(true)
    
    return () => {
      webLayer.dispose()
      webLayerRef.current = null
    }
  }, [position, rotation, size, pixelDensity, interactive])
  
  /**
   * 更新跟随用户逻辑
   */
  useEffect(() => {
    if (!webLayerRef.current || !followUser) return
    
    const updatePosition = () => {
      // 这里需要获取用户头部位置
      // 简化实现，实际应该从XR会话中获取
      const userPosition = new THREE.Vector3(0, 1.6, 0)
      const userRotation = new THREE.Quaternion()
      
      if (webLayerRef.current) {
        // 计算面板应该在用户前方的位置
        const forward = new THREE.Vector3(0, 0, -followDistance)
        forward.applyQuaternion(userRotation)
        
        const newPosition = userPosition.clone().add(forward)
        webLayerRef.current.setPosition(newPosition.x, newPosition.y, newPosition.z)
        
        // 让面板朝向用户
        webLayerRef.current.lookAt(userPosition)
      }
    }
    
    const interval = setInterval(updatePosition, 16) // 60fps
    
    return () => {
      clearInterval(interval)
    }
  }, [followUser, followDistance])
  
  /**
   * 获取Web层对象
   */
  const getWebLayer = (): WebLayer3D | null => {
    return webLayerRef.current
  }
  
  /**
   * 设置面板位置
   */
  const setPosition = (x: number, y: number, z: number): void => {
    if (webLayerRef.current) {
      webLayerRef.current.setPosition(x, y, z)
    }
  }
  
  /**
   * 设置面板旋转
   */
  const setRotation = (x: number, y: number, z: number): void => {
    if (webLayerRef.current) {
      webLayerRef.current.setRotation(x, y, z)
    }
  }
  
  /**
   * 设置面板透明度
   */
  const setOpacity = (opacity: number): void => {
    if (webLayerRef.current) {
      webLayerRef.current.setOpacity(opacity)
    }
  }
  
  return (
    <div
      ref={containerRef}
      className={`xr-ui-panel ${className}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: `${size[0] * 1000}px`,
        height: `${size[1] * 1000}px`,
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderRadius: '8px',
        padding: '20px',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
        fontFamily: 'Arial, sans-serif',
        fontSize: '16px',
        overflow: 'hidden',
        ...style
      }}
    >
      {children}
    </div>
  )
}

/**
 * XR UI按钮组件
 */
export const XRUIButton: React.FC<{
  onClick?: () => void
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'danger'
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
}> = ({
  onClick,
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false
}) => {
  const getButtonStyle = () => {
    const baseStyle = {
      padding: size === 'small' ? '8px 16px' : size === 'large' ? '16px 32px' : '12px 24px',
      fontSize: size === 'small' ? '14px' : size === 'large' ? '18px' : '16px',
      border: 'none',
      borderRadius: '6px',
      cursor: disabled ? 'not-allowed' : 'pointer',
      opacity: disabled ? 0.6 : 1,
      transition: 'all 0.2s ease'
    }
    
    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          backgroundColor: '#1890ff',
          color: 'white'
        }
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: '#f0f0f0',
          color: '#333'
        }
      case 'danger':
        return {
          ...baseStyle,
          backgroundColor: '#ff4d4f',
          color: 'white'
        }
      default:
        return baseStyle
    }
  }
  
  return (
    <button
      style={getButtonStyle()}
      onClick={disabled ? undefined : onClick}
      disabled={disabled}
    >
      {children}
    </button>
  )
}

/**
 * XR UI输入框组件
 */
export const XRUIInput: React.FC<{
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  type?: 'text' | 'password' | 'number'
  disabled?: boolean
}> = ({
  value,
  onChange,
  placeholder,
  type = 'text',
  disabled = false
}) => {
  return (
    <input
      type={type}
      value={value}
      onChange={(e) => onChange?.(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      style={{
        width: '100%',
        padding: '12px',
        fontSize: '16px',
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        outline: 'none',
        backgroundColor: disabled ? '#f5f5f5' : 'white'
      }}
    />
  )
}

/**
 * XR UI文本组件
 */
export const XRUIText: React.FC<{
  children: React.ReactNode
  size?: 'small' | 'medium' | 'large'
  color?: string
  weight?: 'normal' | 'bold'
}> = ({
  children,
  size = 'medium',
  color = '#333',
  weight = 'normal'
}) => {
  const fontSize = size === 'small' ? '14px' : size === 'large' ? '20px' : '16px'
  
  return (
    <span
      style={{
        fontSize,
        color,
        fontWeight: weight
      }}
    >
      {children}
    </span>
  )
}

/**
 * XR UI容器组件
 */
export const XRUIContainer: React.FC<{
  children: React.ReactNode
  direction?: 'row' | 'column'
  gap?: number
  align?: 'start' | 'center' | 'end'
  justify?: 'start' | 'center' | 'end' | 'space-between'
}> = ({
  children,
  direction = 'column',
  gap = 16,
  align = 'start',
  justify = 'start'
}) => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: direction,
        gap: `${gap}px`,
        alignItems: align,
        justifyContent: justify,
        width: '100%'
      }}
    >
      {children}
    </div>
  )
}

export default XRUIPanel
