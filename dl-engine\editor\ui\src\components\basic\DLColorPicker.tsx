/**
 * DL-Engine 颜色选择器组件
 * 
 * 基于 Ant Design ColorPicker 的增强版本
 */

import React, { useState, useCallback } from 'react'
import { ColorPicker, ColorPickerProps, Button, Popover, Space } from 'antd'
import { Color } from 'antd/es/color-picker'
import classNames from 'classnames'
import DLTooltip from './DLTooltip'

/**
 * 预设颜色
 */
const PRESET_COLORS = [
  '#FF0000', '#FF8000', '#FFFF00', '#80FF00', '#00FF00', '#00FF80',
  '#00FFFF', '#0080FF', '#0000FF', '#8000FF', '#FF00FF', '#FF0080',
  '#FFFFFF', '#E0E0E0', '#C0C0C0', '#A0A0A0', '#808080', '#606060',
  '#404040', '#202020', '#000000'
]

/**
 * DL颜色选择器属性
 */
export interface DLColorPickerProps extends Omit<ColorPickerProps, 'onChange'> {
  /** 颜色值 */
  value?: string
  /** 是否显示预设颜色 */
  showPresets?: boolean
  /** 自定义预设颜色 */
  presetColors?: string[]
  /** 是否显示透明度 */
  showAlpha?: boolean
  /** 是否显示颜色格式切换 */
  showFormat?: boolean
  /** 是否显示清除按钮 */
  showClear?: boolean
  /** 颜色变化回调 */
  onChange?: (color: string) => void
  /** 清除回调 */
  onClear?: () => void
  /** 自定义类名 */
  className?: string
}

/**
 * DL颜色选择器组件
 */
const DLColorPicker: React.FC<DLColorPickerProps> = ({
  value,
  showPresets = true,
  presetColors = PRESET_COLORS,
  showAlpha = true,
  showFormat = true,
  showClear = false,
  onChange,
  onClear,
  className,
  ...props
}) => {
  const [open, setOpen] = useState(false)
  const [format, setFormat] = useState<'hex' | 'rgb' | 'hsl'>('hex')
  
  /**
   * 处理颜色变化
   */
  const handleChange = useCallback((color: Color, hex: string) => {
    let colorValue = hex
    
    // 根据格式转换颜色值
    switch (format) {
      case 'rgb':
        const rgb = color.toRgb()
        colorValue = `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`
        break
      case 'hsl':
        const hsl = color.toHsl()
        colorValue = `hsl(${Math.round(hsl.h)}, ${Math.round(hsl.s * 100)}%, ${Math.round(hsl.l * 100)}%)`
        break
      default:
        colorValue = hex
    }
    
    onChange?.(colorValue)
  }, [format, onChange])
  
  /**
   * 处理预设颜色点击
   */
  const handlePresetClick = useCallback((color: string) => {
    onChange?.(color)
    setOpen(false)
  }, [onChange])
  
  /**
   * 处理清除
   */
  const handleClear = useCallback(() => {
    onClear?.()
    setOpen(false)
  }, [onClear])
  
  /**
   * 渲染预设颜色
   */
  const renderPresets = () => {
    if (!showPresets) return null
    
    return (
      <div className="dl-color-picker-presets">
        <div className="text-xs text-gray-500 mb-2">预设颜色</div>
        <div className="grid grid-cols-7 gap-1">
          {presetColors.map((color, index) => (
            <DLTooltip key={index} title={color}>
              <div
                className="w-6 h-6 rounded cursor-pointer border border-gray-300 hover:border-gray-400"
                style={{ backgroundColor: color }}
                onClick={() => handlePresetClick(color)}
              />
            </DLTooltip>
          ))}
        </div>
      </div>
    )
  }
  
  /**
   * 渲染格式切换
   */
  const renderFormatSwitch = () => {
    if (!showFormat) return null
    
    return (
      <div className="dl-color-picker-format">
        <div className="text-xs text-gray-500 mb-2">颜色格式</div>
        <Space.Compact>
          {(['hex', 'rgb', 'hsl'] as const).map((fmt) => (
            <Button
              key={fmt}
              size="small"
              type={format === fmt ? 'primary' : 'default'}
              onClick={() => setFormat(fmt)}
            >
              {fmt.toUpperCase()}
            </Button>
          ))}
        </Space.Compact>
      </div>
    )
  }
  
  /**
   * 渲染操作按钮
   */
  const renderActions = () => {
    if (!showClear) return null
    
    return (
      <div className="dl-color-picker-actions">
        <Button size="small" onClick={handleClear}>
          清除
        </Button>
      </div>
    )
  }
  
  /**
   * 渲染弹出内容
   */
  const renderContent = () => {
    return (
      <div className="dl-color-picker-content">
        <ColorPicker
          {...props}
          value={value}
          onChange={handleChange}
          showText={false}
          size="large"
          disabledAlpha={!showAlpha}
        />
        
        {(showPresets || showFormat || showClear) && (
          <div className="mt-3 space-y-3">
            {renderPresets()}
            {renderFormatSwitch()}
            {renderActions()}
          </div>
        )}
      </div>
    )
  }
  
  /**
   * 颜色选择器类名
   */
  const colorPickerClassName = classNames(
    'dl-color-picker',
    className
  )
  
  return (
    <Popover
      content={renderContent()}
      trigger="click"
      open={open}
      onOpenChange={setOpen}
      placement="bottomLeft"
    >
      <div className={colorPickerClassName}>
        <div className="dl-color-picker-trigger">
          <div
            className="w-8 h-8 rounded border border-gray-300 cursor-pointer flex items-center justify-center"
            style={{ backgroundColor: value || '#ffffff' }}
          >
            {!value && (
              <div className="w-6 h-0.5 bg-red-500 rotate-45" />
            )}
          </div>
        </div>
      </div>
    </Popover>
  )
}

export default DLColorPicker
