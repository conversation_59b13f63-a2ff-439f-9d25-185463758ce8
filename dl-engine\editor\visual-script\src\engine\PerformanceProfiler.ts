/**
 * 可视化脚本性能分析器
 * 
 * 提供脚本执行性能分析和优化建议
 */

export interface NodePerformanceData {
  nodeId: string
  nodeType: string
  executionCount: number
  totalTime: number
  averageTime: number
  minTime: number
  maxTime: number
  lastExecutionTime: number
}

export interface PerformanceReport {
  totalExecutionTime: number
  totalNodes: number
  executedNodes: number
  averageNodeTime: number
  slowestNodes: NodePerformanceData[]
  hottestNodes: NodePerformanceData[]
  recommendations: string[]
}

export interface PerformanceThresholds {
  slowNodeThreshold: number // 毫秒
  hotNodeThreshold: number // 执行次数
  totalTimeThreshold: number // 毫秒
}

/**
 * 性能分析器类
 */
export class PerformanceProfiler {
  private nodeData: Map<string, NodePerformanceData> = new Map()
  private sessionStartTime = 0
  private sessionEndTime = 0
  private isRecording = false
  private thresholds: PerformanceThresholds

  constructor(thresholds?: Partial<PerformanceThresholds>) {
    this.thresholds = {
      slowNodeThreshold: 100, // 100ms
      hotNodeThreshold: 100,  // 100次执行
      totalTimeThreshold: 5000, // 5秒
      ...thresholds
    }
  }

  /**
   * 开始性能记录
   */
  startRecording(): void {
    this.isRecording = true
    this.sessionStartTime = performance.now()
    this.nodeData.clear()
  }

  /**
   * 停止性能记录
   */
  stopRecording(): void {
    this.isRecording = false
    this.sessionEndTime = performance.now()
  }

  /**
   * 记录节点执行
   */
  recordNodeExecution(nodeId: string, nodeType: string, executionTime: number): void {
    if (!this.isRecording) return

    let data = this.nodeData.get(nodeId)
    
    if (!data) {
      data = {
        nodeId,
        nodeType,
        executionCount: 0,
        totalTime: 0,
        averageTime: 0,
        minTime: Infinity,
        maxTime: 0,
        lastExecutionTime: 0
      }
      this.nodeData.set(nodeId, data)
    }

    // 更新统计数据
    data.executionCount++
    data.totalTime += executionTime
    data.averageTime = data.totalTime / data.executionCount
    data.minTime = Math.min(data.minTime, executionTime)
    data.maxTime = Math.max(data.maxTime, executionTime)
    data.lastExecutionTime = executionTime
  }

  /**
   * 生成性能报告
   */
  generateReport(): PerformanceReport {
    const nodeDataArray = Array.from(this.nodeData.values())
    const totalExecutionTime = this.sessionEndTime - this.sessionStartTime
    const executedNodes = nodeDataArray.length
    const totalNodes = nodeDataArray.length // 这里应该是图中的总节点数

    // 计算平均节点执行时间
    const totalNodeTime = nodeDataArray.reduce((sum, data) => sum + data.totalTime, 0)
    const averageNodeTime = executedNodes > 0 ? totalNodeTime / executedNodes : 0

    // 找出最慢的节点（按平均执行时间排序）
    const slowestNodes = nodeDataArray
      .filter(data => data.averageTime > this.thresholds.slowNodeThreshold)
      .sort((a, b) => b.averageTime - a.averageTime)
      .slice(0, 10)

    // 找出最热的节点（按执行次数排序）
    const hottestNodes = nodeDataArray
      .filter(data => data.executionCount > this.thresholds.hotNodeThreshold)
      .sort((a, b) => b.executionCount - a.executionCount)
      .slice(0, 10)

    // 生成优化建议
    const recommendations = this.generateRecommendations(
      nodeDataArray,
      totalExecutionTime,
      slowestNodes,
      hottestNodes
    )

    return {
      totalExecutionTime,
      totalNodes,
      executedNodes,
      averageNodeTime,
      slowestNodes,
      hottestNodes,
      recommendations
    }
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(
    nodeDataArray: NodePerformanceData[],
    totalExecutionTime: number,
    slowestNodes: NodePerformanceData[],
    hottestNodes: NodePerformanceData[]
  ): string[] {
    const recommendations: string[] = []

    // 检查总执行时间
    if (totalExecutionTime > this.thresholds.totalTimeThreshold) {
      recommendations.push(
        `脚本总执行时间过长 (${totalExecutionTime.toFixed(2)}ms)，建议优化脚本逻辑`
      )
    }

    // 检查慢节点
    if (slowestNodes.length > 0) {
      const slowestNode = slowestNodes[0]
      recommendations.push(
        `节点 ${slowestNode.nodeId} (${slowestNode.nodeType}) 执行时间过长 ` +
        `(平均 ${slowestNode.averageTime.toFixed(2)}ms)，建议检查节点逻辑`
      )
    }

    // 检查热点节点
    if (hottestNodes.length > 0) {
      const hottestNode = hottestNodes[0]
      recommendations.push(
        `节点 ${hottestNode.nodeId} (${hottestNode.nodeType}) 执行次数过多 ` +
        `(${hottestNode.executionCount} 次)，可能存在不必要的循环`
      )
    }

    // 检查重复执行的慢节点
    const slowAndHotNodes = nodeDataArray.filter(
      data => data.averageTime > this.thresholds.slowNodeThreshold / 2 &&
              data.executionCount > this.thresholds.hotNodeThreshold / 2
    )

    if (slowAndHotNodes.length > 0) {
      recommendations.push(
        `发现 ${slowAndHotNodes.length} 个既慢又热的节点，这些是优化的重点目标`
      )
    }

    // 检查内存使用（如果有相关数据）
    const highMemoryNodes = nodeDataArray.filter(data => 
      data.nodeType.includes('Array') || data.nodeType.includes('Object')
    )

    if (highMemoryNodes.length > 5) {
      recommendations.push(
        '脚本中使用了大量数组/对象操作节点，注意内存使用情况'
      )
    }

    // 通用建议
    if (recommendations.length === 0) {
      recommendations.push('脚本性能良好，无明显优化点')
    } else {
      recommendations.push('建议使用调试器逐步分析慢节点的具体原因')
      recommendations.push('考虑将复杂逻辑拆分为多个简单节点')
    }

    return recommendations
  }

  /**
   * 获取节点性能数据
   */
  getNodeData(nodeId: string): NodePerformanceData | undefined {
    return this.nodeData.get(nodeId)
  }

  /**
   * 获取所有节点性能数据
   */
  getAllNodeData(): NodePerformanceData[] {
    return Array.from(this.nodeData.values())
  }

  /**
   * 清除性能数据
   */
  clearData(): void {
    this.nodeData.clear()
    this.sessionStartTime = 0
    this.sessionEndTime = 0
  }

  /**
   * 导出性能数据
   */
  exportData(): string {
    const report = this.generateReport()
    return JSON.stringify(report, null, 2)
  }

  /**
   * 导入性能数据
   */
  importData(data: string): void {
    try {
      const report = JSON.parse(data) as PerformanceReport
      this.nodeData.clear()
      
      // 重建节点数据
      [...report.slowestNodes, ...report.hottestNodes].forEach(nodeData => {
        this.nodeData.set(nodeData.nodeId, nodeData)
      })
    } catch (error) {
      throw new Error(`导入性能数据失败: ${error}`)
    }
  }

  /**
   * 设置性能阈值
   */
  setThresholds(thresholds: Partial<PerformanceThresholds>): void {
    this.thresholds = { ...this.thresholds, ...thresholds }
  }

  /**
   * 获取性能阈值
   */
  getThresholds(): PerformanceThresholds {
    return { ...this.thresholds }
  }

  /**
   * 是否正在记录
   */
  isRecordingActive(): boolean {
    return this.isRecording
  }

  /**
   * 获取会话时长
   */
  getSessionDuration(): number {
    if (this.isRecording) {
      return performance.now() - this.sessionStartTime
    }
    return this.sessionEndTime - this.sessionStartTime
  }

  /**
   * 获取性能摘要
   */
  getSummary(): {
    totalNodes: number
    executedNodes: number
    totalTime: number
    averageTime: number
    slowNodes: number
    hotNodes: number
  } {
    const nodeDataArray = Array.from(this.nodeData.values())
    const totalTime = nodeDataArray.reduce((sum, data) => sum + data.totalTime, 0)
    
    return {
      totalNodes: nodeDataArray.length,
      executedNodes: nodeDataArray.length,
      totalTime,
      averageTime: nodeDataArray.length > 0 ? totalTime / nodeDataArray.length : 0,
      slowNodes: nodeDataArray.filter(data => data.averageTime > this.thresholds.slowNodeThreshold).length,
      hotNodes: nodeDataArray.filter(data => data.executionCount > this.thresholds.hotNodeThreshold).length
    }
  }
}

export default PerformanceProfiler
