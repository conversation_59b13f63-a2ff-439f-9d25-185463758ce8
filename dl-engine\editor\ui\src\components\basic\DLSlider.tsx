/**
 * DL-Engine 滑块组件
 * 
 * 基于 Ant Design Slider 的增强版本
 */

import React, { useState, useCallback } from 'react'
import { Slider, SliderSingleProps, InputNumber } from 'antd'
import classNames from 'classnames'

/**
 * DL滑块属性
 */
export interface DLSliderProps extends SliderSingleProps {
  /** 滑块标签 */
  label?: string
  /** 是否显示数值输入框 */
  showInput?: boolean
  /** 是否显示当前值 */
  showValue?: boolean
  /** 数值格式化函数 */
  formatter?: (value: number) => string
  /** 单位 */
  unit?: string
  /** 自定义类名 */
  className?: string
}

/**
 * DL滑块组件
 */
const DLSlider: React.FC<DLSliderProps> = ({
  label,
  showInput = false,
  showValue = true,
  formatter,
  unit,
  value,
  onChange,
  className,
  ...props
}) => {
  const [internalValue, setInternalValue] = useState<number>(value || props.defaultValue || 0)
  
  /**
   * 处理滑块值变化
   */
  const handleSliderChange = useCallback((newValue: number) => {
    setInternalValue(newValue)
    onChange?.(newValue)
  }, [onChange])
  
  /**
   * 处理输入框值变化
   */
  const handleInputChange = useCallback((newValue: number | null) => {
    if (newValue !== null) {
      setInternalValue(newValue)
      onChange?.(newValue)
    }
  }, [onChange])
  
  /**
   * 格式化显示值
   */
  const formatValue = useCallback((val: number) => {
    if (formatter) {
      return formatter(val)
    }
    return unit ? `${val}${unit}` : val.toString()
  }, [formatter, unit])
  
  /**
   * 当前值
   */
  const currentValue = value !== undefined ? value : internalValue
  
  /**
   * 滑块类名
   */
  const sliderClassName = classNames(
    'dl-slider',
    {
      'dl-slider--with-label': label,
      'dl-slider--with-input': showInput
    },
    className
  )
  
  return (
    <div className={sliderClassName}>
      {/* 标签 */}
      {label && (
        <div className="dl-slider-header flex items-center justify-between mb-2">
          <span className="text-sm text-gray-700">{label}</span>
          {showValue && (
            <span className="text-sm text-gray-500">
              {formatValue(currentValue)}
            </span>
          )}
        </div>
      )}
      
      {/* 滑块和输入框 */}
      <div className="dl-slider-content flex items-center">
        <div className="flex-1">
          <Slider
            {...props}
            value={currentValue}
            onChange={handleSliderChange}
          />
        </div>
        
        {showInput && (
          <div className="ml-3">
            <InputNumber
              size="small"
              value={currentValue}
              onChange={handleInputChange}
              min={props.min}
              max={props.max}
              step={props.step}
              style={{ width: 80 }}
            />
          </div>
        )}
      </div>
    </div>
  )
}

export default DLSlider
