/**
 * DL-Engine 面板组件
 * 
 * 可折叠、可拖拽的面板容器
 */

import React, { useState, useCallback } from 'react'
import { Card, CardProps, Button, Space, Dropdown, MenuProps } from 'antd'
import { useTranslation } from 'react-i18next'
import { 
  ExpandOutlined, 
  CompressOutlined, 
  CloseOutlined, 
  MoreOutlined,
  PushpinOutlined,
  PushpinFilled
} from '@ant-design/icons'
import classNames from 'classnames'
import DLIcon from '../basic/DLIcon'
import DLTooltip from '../basic/DLTooltip'

/**
 * 面板状态
 */
export type DLPanelState = 'normal' | 'collapsed' | 'maximized' | 'minimized'

/**
 * DL面板属性
 */
export interface DLPanelProps extends Omit<CardProps, 'title'> {
  /** 面板标题 */
  title?: string | React.ReactNode
  /** 面板图标 */
  icon?: string | React.ReactNode
  /** 面板状态 */
  state?: DLPanelState
  /** 是否可折叠 */
  collapsible?: boolean
  /** 是否可最大化 */
  maximizable?: boolean
  /** 是否可关闭 */
  closable?: boolean
  /** 是否可固定 */
  pinnable?: boolean
  /** 是否已固定 */
  pinned?: boolean
  /** 是否显示工具栏 */
  showToolbar?: boolean
  /** 工具栏额外操作 */
  toolbarActions?: React.ReactNode
  /** 面板菜单项 */
  menuItems?: MenuProps['items']
  /** 状态变化回调 */
  onStateChange?: (state: DLPanelState) => void
  /** 关闭回调 */
  onClose?: () => void
  /** 固定状态变化回调 */
  onPinChange?: (pinned: boolean) => void
  /** 菜单点击回调 */
  onMenuClick?: (key: string) => void
  /** 自定义类名 */
  className?: string
  /** 子组件 */
  children?: React.ReactNode
}

/**
 * DL面板组件
 */
const DLPanel: React.FC<DLPanelProps> = ({
  title,
  icon,
  state = 'normal',
  collapsible = true,
  maximizable = true,
  closable = false,
  pinnable = false,
  pinned = false,
  showToolbar = true,
  toolbarActions,
  menuItems,
  onStateChange,
  onClose,
  onPinChange,
  onMenuClick,
  className,
  children,
  ...props
}) => {
  const { t } = useTranslation()
  const [collapsed, setCollapsed] = useState(state === 'collapsed')
  const [maximized, setMaximized] = useState(state === 'maximized')
  
  /**
   * 处理折叠/展开
   */
  const handleToggleCollapse = useCallback(() => {
    const newCollapsed = !collapsed
    setCollapsed(newCollapsed)
    onStateChange?.(newCollapsed ? 'collapsed' : 'normal')
  }, [collapsed, onStateChange])
  
  /**
   * 处理最大化/还原
   */
  const handleToggleMaximize = useCallback(() => {
    const newMaximized = !maximized
    setMaximized(newMaximized)
    onStateChange?.(newMaximized ? 'maximized' : 'normal')
  }, [maximized, onStateChange])
  
  /**
   * 处理关闭
   */
  const handleClose = useCallback(() => {
    onClose?.()
  }, [onClose])
  
  /**
   * 处理固定
   */
  const handleTogglePin = useCallback(() => {
    onPinChange?.(!pinned)
  }, [pinned, onPinChange])
  
  /**
   * 处理菜单点击
   */
  const handleMenuClick: MenuProps['onClick'] = useCallback(({ key }) => {
    onMenuClick?.(key)
  }, [onMenuClick])
  
  /**
   * 渲染标题图标
   */
  const renderTitleIcon = () => {
    if (!icon) return null
    
    if (typeof icon === 'string') {
      return <DLIcon name={icon} className="mr-2" />
    }
    
    return <span className="mr-2">{icon}</span>
  }
  
  /**
   * 渲染工具栏
   */
  const renderToolbar = () => {
    if (!showToolbar) return null
    
    const actions = []
    
    // 自定义操作
    if (toolbarActions) {
      actions.push(toolbarActions)
    }
    
    // 固定按钮
    if (pinnable) {
      actions.push(
        <DLTooltip key="pin" title={pinned ? '取消固定' : '固定面板'}>
          <Button
            type="text"
            size="small"
            icon={pinned ? <PushpinFilled /> : <PushpinOutlined />}
            onClick={handleTogglePin}
          />
        </DLTooltip>
      )
    }
    
    // 最大化按钮
    if (maximizable) {
      actions.push(
        <DLTooltip key="maximize" title={maximized ? '还原' : '最大化'}>
          <Button
            type="text"
            size="small"
            icon={maximized ? <CompressOutlined /> : <ExpandOutlined />}
            onClick={handleToggleMaximize}
          />
        </DLTooltip>
      )
    }
    
    // 折叠按钮
    if (collapsible) {
      actions.push(
        <DLTooltip key="collapse" title={collapsed ? '展开' : '折叠'}>
          <Button
            type="text"
            size="small"
            icon={<DLIcon name={collapsed ? 'chevron-down' : 'chevron-up'} />}
            onClick={handleToggleCollapse}
          />
        </DLTooltip>
      )
    }
    
    // 菜单按钮
    if (menuItems && menuItems.length > 0) {
      actions.push(
        <Dropdown
          key="menu"
          menu={{ items: menuItems, onClick: handleMenuClick }}
          trigger={['click']}
        >
          <Button
            type="text"
            size="small"
            icon={<MoreOutlined />}
          />
        </Dropdown>
      )
    }
    
    // 关闭按钮
    if (closable) {
      actions.push(
        <DLTooltip key="close" title="关闭">
          <Button
            type="text"
            size="small"
            icon={<CloseOutlined />}
            onClick={handleClose}
          />
        </DLTooltip>
      )
    }
    
    return (
      <Space size="small">
        {actions}
      </Space>
    )
  }
  
  /**
   * 渲染标题
   */
  const renderTitle = () => {
    return (
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center">
          {renderTitleIcon()}
          <span>{title}</span>
        </div>
        {renderToolbar()}
      </div>
    )
  }
  
  /**
   * 面板类名
   */
  const panelClassName = classNames(
    'dl-panel',
    {
      'dl-panel--collapsed': collapsed,
      'dl-panel--maximized': maximized,
      'dl-panel--pinned': pinned,
      'dl-panel--closable': closable
    },
    className
  )
  
  return (
    <Card
      {...props}
      className={panelClassName}
      title={title ? renderTitle() : undefined}
      size="small"
      bodyStyle={{
        display: collapsed ? 'none' : 'block',
        padding: collapsed ? 0 : undefined,
        ...props.bodyStyle
      }}
    >
      {!collapsed && children}
    </Card>
  )
}

export default DLPanel
