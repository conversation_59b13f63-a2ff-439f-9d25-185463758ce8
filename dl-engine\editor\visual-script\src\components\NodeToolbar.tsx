/**
 * 节点工具栏组件
 * 
 * 提供节点编辑器的工具栏功能
 */

import React from 'react'
import { Button, Space, Tooltip, Divider } from 'antd'
import { useTranslation } from 'react-i18next'
import { 
  ZoomInOutlined, 
  ZoomOutOutlined, 
  BorderOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  BugOutlined,
  SaveOutlined,
  UndoOutlined,
  RedoOutlined
} from '@ant-design/icons'
import { NodeEditorState, ExecutionState } from '../types'

/**
 * 节点工具栏属性
 */
export interface NodeToolbarProps {
  /** 编辑器状态 */
  editorState: NodeEditorState
  /** 执行状态 */
  executionState?: ExecutionState
  /** 是否只读 */
  readonly?: boolean
  /** 放大回调 */
  onZoomIn?: () => void
  /** 缩小回调 */
  onZoomOut?: () => void
  /** 重置缩放回调 */
  onZoomReset?: () => void
  /** 适应屏幕回调 */
  onFitToScreen?: () => void
  /** 执行回调 */
  onExecute?: () => void
  /** 暂停回调 */
  onPause?: () => void
  /** 停止回调 */
  onStop?: () => void
  /** 调试回调 */
  onDebug?: () => void
  /** 保存回调 */
  onSave?: () => void
  /** 撤销回调 */
  onUndo?: () => void
  /** 重做回调 */
  onRedo?: () => void
  /** 自定义类名 */
  className?: string
}

/**
 * 节点工具栏组件
 */
const NodeToolbar: React.FC<NodeToolbarProps> = ({
  editorState,
  executionState = ExecutionState.IDLE,
  readonly = false,
  onZoomIn,
  onZoomOut,
  onZoomReset,
  onFitToScreen,
  onExecute,
  onPause,
  onStop,
  onDebug,
  onSave,
  onUndo,
  onRedo,
  className = ''
}) => {
  const { t } = useTranslation()
  
  /**
   * 格式化缩放百分比
   */
  const formatZoomPercentage = (zoom: number): string => {
    return `${Math.round(zoom * 100)}%`
  }
  
  /**
   * 获取执行按钮
   */
  const getExecutionButton = () => {
    switch (executionState) {
      case ExecutionState.RUNNING:
        return (
          <Tooltip title={t('editor.pause')}>
            <Button
              icon={<PauseCircleOutlined />}
              onClick={onPause}
              disabled={readonly}
            />
          </Tooltip>
        )
      
      case ExecutionState.PAUSED:
        return (
          <Tooltip title={t('editor.resume')}>
            <Button
              icon={<PlayCircleOutlined />}
              onClick={onExecute}
              disabled={readonly}
            />
          </Tooltip>
        )
      
      default:
        return (
          <Tooltip title={t('editor.execute')}>
            <Button
              icon={<PlayCircleOutlined />}
              onClick={onExecute}
              disabled={readonly}
              type="primary"
            />
          </Tooltip>
        )
    }
  }
  
  return (
    <div className={`node-toolbar ${className}`}>
      <div className="toolbar-content flex items-center justify-between p-2 bg-white border-b border-gray-200">
        {/* 左侧工具 */}
        <Space>
          {/* 编辑工具 */}
          {!readonly && (
            <>
              <Tooltip title={t('common.undo')}>
                <Button
                  icon={<UndoOutlined />}
                  onClick={onUndo}
                  size="small"
                />
              </Tooltip>
              
              <Tooltip title={t('common.redo')}>
                <Button
                  icon={<RedoOutlined />}
                  onClick={onRedo}
                  size="small"
                />
              </Tooltip>
              
              <Divider type="vertical" />
            </>
          )}
          
          {/* 视图工具 */}
          <Tooltip title={t('editor.zoomIn')}>
            <Button
              icon={<ZoomInOutlined />}
              onClick={onZoomIn}
              size="small"
            />
          </Tooltip>
          
          <Tooltip title={formatZoomPercentage(editorState.zoom)}>
            <Button
              onClick={onZoomReset}
              size="small"
              style={{ minWidth: '60px' }}
            >
              {formatZoomPercentage(editorState.zoom)}
            </Button>
          </Tooltip>
          
          <Tooltip title={t('editor.zoomOut')}>
            <Button
              icon={<ZoomOutOutlined />}
              onClick={onZoomOut}
              size="small"
            />
          </Tooltip>
          
          <Tooltip title={t('editor.fitToScreen')}>
            <Button
              icon={<BorderOutlined />}
              onClick={onFitToScreen}
              size="small"
            />
          </Tooltip>
        </Space>
        
        {/* 中间状态显示 */}
        <div className="toolbar-status">
          {executionState !== ExecutionState.IDLE && (
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                executionState === ExecutionState.RUNNING ? 'bg-green-500 animate-pulse' :
                executionState === ExecutionState.PAUSED ? 'bg-yellow-500' :
                executionState === ExecutionState.ERROR ? 'bg-red-500' :
                'bg-gray-500'
              }`} />
              <span className="text-sm text-gray-600">
                {executionState === ExecutionState.RUNNING ? t('editor.running') :
                 executionState === ExecutionState.PAUSED ? t('editor.paused') :
                 executionState === ExecutionState.ERROR ? t('editor.error') :
                 t('editor.idle')}
              </span>
            </div>
          )}
        </div>
        
        {/* 右侧工具 */}
        <Space>
          {/* 执行控制 */}
          {!readonly && (
            <>
              {getExecutionButton()}
              
              {executionState !== ExecutionState.IDLE && (
                <Tooltip title={t('editor.stop')}>
                  <Button
                    icon={<StopOutlined />}
                    onClick={onStop}
                    danger
                  />
                </Tooltip>
              )}
              
              <Tooltip title={t('editor.debug')}>
                <Button
                  icon={<BugOutlined />}
                  onClick={onDebug}
                />
              </Tooltip>
              
              <Divider type="vertical" />
              
              {/* 保存 */}
              <Tooltip title={t('common.save')}>
                <Button
                  icon={<SaveOutlined />}
                  onClick={onSave}
                  type="primary"
                  ghost
                />
              </Tooltip>
            </>
          )}
        </Space>
      </div>
    </div>
  )
}

export default NodeToolbar
