/**
 * DL-Engine 对话框组件
 * 
 * 基于 Ant Design Modal 的增强版本，支持多种对话框类型
 */

import React, { useCallback, useState } from 'react'
import { Modal, ModalProps, Button, Space, Result, Input, Form } from 'antd'
import { useTranslation } from 'react-i18next'
import { ExclamationCircleOutlined, CheckCircleOutlined, CloseCircleOutlined, InfoCircleOutlined } from '@ant-design/icons'
import classNames from 'classnames'

/**
 * 对话框类型
 */
export type DLModalType = 'default' | 'confirm' | 'info' | 'success' | 'warning' | 'error' | 'prompt'

/**
 * DL对话框属性
 */
export interface DLModalProps extends Omit<ModalProps, 'onOk'> {
  /** 对话框类型 */
  type?: DLModalType
  /** 是否显示图标 */
  showIcon?: boolean
  /** 确认按钮文本 */
  okText?: string
  /** 取消按钮文本 */
  cancelText?: string
  /** 是否显示取消按钮 */
  showCancel?: boolean
  /** 确认回调 */
  onOk?: (value?: any) => void | Promise<void>
  /** 取消回调 */
  onCancel?: () => void
  /** 输入提示（用于prompt类型） */
  inputPlaceholder?: string
  /** 输入默认值（用于prompt类型） */
  inputDefaultValue?: string
  /** 输入验证规则（用于prompt类型） */
  inputRules?: any[]
  /** 自定义类名 */
  className?: string
}

/**
 * DL对话框组件
 */
const DLModal: React.FC<DLModalProps> = ({
  type = 'default',
  showIcon = true,
  okText,
  cancelText,
  showCancel = true,
  onOk,
  onCancel,
  inputPlaceholder,
  inputDefaultValue = '',
  inputRules = [],
  className,
  children,
  ...props
}) => {
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [inputValue, setInputValue] = useState(inputDefaultValue)
  const [form] = Form.useForm()
  
  /**
   * 获取图标
   */
  const getIcon = () => {
    if (!showIcon) return null
    
    switch (type) {
      case 'confirm':
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      case 'error':
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />
      case 'info':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />
      default:
        return null
    }
  }
  
  /**
   * 处理确认
   */
  const handleOk = useCallback(async () => {
    if (type === 'prompt') {
      try {
        await form.validateFields()
        const value = form.getFieldValue('input')
        setLoading(true)
        await onOk?.(value)
      } catch (error) {
        return // 验证失败，不关闭对话框
      } finally {
        setLoading(false)
      }
    } else {
      try {
        setLoading(true)
        await onOk?.()
      } finally {
        setLoading(false)
      }
    }
  }, [type, form, onOk])
  
  /**
   * 处理取消
   */
  const handleCancel = useCallback(() => {
    onCancel?.()
  }, [onCancel])
  
  /**
   * 渲染内容
   */
  const renderContent = () => {
    if (type === 'prompt') {
      return (
        <Form
          form={form}
          layout="vertical"
          initialValues={{ input: inputDefaultValue }}
        >
          <Form.Item
            name="input"
            rules={[
              { required: true, message: '请输入内容' },
              ...inputRules
            ]}
          >
            <Input
              placeholder={inputPlaceholder || '请输入'}
              autoFocus
            />
          </Form.Item>
        </Form>
      )
    }
    
    if (['confirm', 'info', 'success', 'warning', 'error'].includes(type)) {
      return (
        <div className="flex items-start space-x-3">
          {getIcon()}
          <div className="flex-1">
            {children}
          </div>
        </div>
      )
    }
    
    return children
  }
  
  /**
   * 获取默认按钮文本
   */
  const getDefaultOkText = () => {
    switch (type) {
      case 'confirm':
        return t('common.confirm')
      case 'error':
        return t('common.ok')
      default:
        return t('common.ok')
    }
  }
  
  /**
   * 对话框类名
   */
  const modalClassName = classNames(
    'dl-modal',
    `dl-modal--${type}`,
    {
      'dl-modal--with-icon': showIcon && ['confirm', 'info', 'success', 'warning', 'error'].includes(type)
    },
    className
  )
  
  /**
   * 自定义页脚
   */
  const footer = (
    <Space>
      {showCancel && (
        <Button onClick={handleCancel}>
          {cancelText || t('common.cancel')}
        </Button>
      )}
      <Button
        type="primary"
        loading={loading}
        onClick={handleOk}
        danger={type === 'error'}
      >
        {okText || getDefaultOkText()}
      </Button>
    </Space>
  )
  
  return (
    <Modal
      {...props}
      className={modalClassName}
      onCancel={handleCancel}
      footer={footer}
      destroyOnClose
    >
      {renderContent()}
    </Modal>
  )
}

/**
 * 静态方法：确认对话框
 */
DLModal.confirm = (config: Omit<DLModalProps, 'open'>) => {
  return new Promise<boolean>((resolve) => {
    const modal = Modal.confirm({
      ...config,
      onOk: async () => {
        try {
          await config.onOk?.()
          resolve(true)
        } catch (error) {
          resolve(false)
        }
      },
      onCancel: () => {
        config.onCancel?.()
        resolve(false)
      }
    })
  })
}

/**
 * 静态方法：信息对话框
 */
DLModal.info = (config: Omit<DLModalProps, 'open'>) => {
  return Modal.info({
    ...config,
    icon: <InfoCircleOutlined />
  })
}

/**
 * 静态方法：成功对话框
 */
DLModal.success = (config: Omit<DLModalProps, 'open'>) => {
  return Modal.success({
    ...config,
    icon: <CheckCircleOutlined />
  })
}

/**
 * 静态方法：警告对话框
 */
DLModal.warning = (config: Omit<DLModalProps, 'open'>) => {
  return Modal.warning({
    ...config,
    icon: <ExclamationCircleOutlined />
  })
}

/**
 * 静态方法：错误对话框
 */
DLModal.error = (config: Omit<DLModalProps, 'open'>) => {
  return Modal.error({
    ...config,
    icon: <CloseCircleOutlined />
  })
}

/**
 * 静态方法：输入对话框
 */
DLModal.prompt = (config: Omit<DLModalProps, 'open' | 'type'>) => {
  return new Promise<string | null>((resolve) => {
    let inputValue = config.inputDefaultValue || ''
    
    const modal = Modal.confirm({
      ...config,
      content: (
        <Input
          placeholder={config.inputPlaceholder || '请输入'}
          defaultValue={inputValue}
          onChange={(e) => { inputValue = e.target.value }}
          autoFocus
        />
      ),
      onOk: async () => {
        try {
          await config.onOk?.(inputValue)
          resolve(inputValue)
        } catch (error) {
          resolve(null)
        }
      },
      onCancel: () => {
        config.onCancel?.()
        resolve(null)
      }
    })
  })
}

export default DLModal
