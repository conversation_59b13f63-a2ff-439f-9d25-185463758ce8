/**
 * 插槽渲染器组件
 * 
 * 渲染节点的输入/输出插槽
 */

import React, { useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'
import { NodeSocket, DataType } from '../types'

/**
 * 插槽渲染器属性
 */
export interface SocketRendererProps {
  /** 插槽定义 */
  socket: NodeSocket
  /** 节点ID */
  nodeId: string
  /** 插槽类型 */
  type: 'input' | 'output'
  /** 是否已连接 */
  connected: boolean
  /** 是否悬停 */
  hovered?: boolean
  /** 连接开始回调 */
  onConnectionStart?: () => void
  /** 连接结束回调 */
  onConnectionEnd?: () => void
  /** 悬停回调 */
  onHover?: (hovered: boolean) => void
  /** 自定义类名 */
  className?: string
}

/**
 * 获取数据类型颜色
 */
const getDataTypeColor = (dataType: DataType): string => {
  switch (dataType) {
    case DataType.EXECUTION:
      return '#ffffff'
    case DataType.BOOLEAN:
      return '#ff6b6b'
    case DataType.NUMBER:
      return '#4ecdc4'
    case DataType.STRING:
      return '#ffe66d'
    case DataType.VECTOR2:
      return '#a8e6cf'
    case DataType.VECTOR3:
      return '#88d8c0'
    case DataType.COLOR:
      return '#ffd93d'
    case DataType.OBJECT:
      return '#6c5ce7'
    case DataType.ANY:
      return '#95a5a6'
    default:
      return '#bdc3c7'
  }
}

/**
 * 获取插槽形状
 */
const getSocketShape = (dataType: DataType, isExecution: boolean): 'circle' | 'square' | 'diamond' => {
  if (isExecution) return 'diamond'
  
  switch (dataType) {
    case DataType.BOOLEAN:
      return 'square'
    case DataType.OBJECT:
      return 'diamond'
    default:
      return 'circle'
  }
}

/**
 * 插槽渲染器组件
 */
const SocketRenderer: React.FC<SocketRendererProps> = ({
  socket,
  nodeId,
  type,
  connected,
  hovered = false,
  onConnectionStart,
  onConnectionEnd,
  onHover,
  className = ''
}) => {
  const { t } = useTranslation()
  
  /**
   * 处理鼠标按下
   */
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    event.stopPropagation()
    onConnectionStart?.()
  }, [onConnectionStart])
  
  /**
   * 处理鼠标抬起
   */
  const handleMouseUp = useCallback((event: React.MouseEvent) => {
    event.stopPropagation()
    onConnectionEnd?.()
  }, [onConnectionEnd])
  
  /**
   * 处理鼠标进入
   */
  const handleMouseEnter = useCallback(() => {
    onHover?.(true)
  }, [onHover])
  
  /**
   * 处理鼠标离开
   */
  const handleMouseLeave = useCallback(() => {
    onHover?.(false)
  }, [onHover])
  
  /**
   * 获取插槽样式
   */
  const getSocketStyle = () => {
    const color = getDataTypeColor(socket.dataType)
    const shape = getSocketShape(socket.dataType, socket.isExecution)
    const size = socket.isExecution ? 12 : 10
    
    const baseStyle = {
      width: `${size}px`,
      height: `${size}px`,
      backgroundColor: connected ? color : 'transparent',
      border: `2px solid ${color}`,
      cursor: 'pointer',
      transition: 'all 0.2s ease'
    }
    
    switch (shape) {
      case 'circle':
        return {
          ...baseStyle,
          borderRadius: '50%'
        }
      case 'square':
        return {
          ...baseStyle,
          borderRadius: '2px'
        }
      case 'diamond':
        return {
          ...baseStyle,
          borderRadius: '2px',
          transform: 'rotate(45deg)'
        }
      default:
        return baseStyle
    }
  }
  
  /**
   * 插槽类名
   */
  const socketClassName = classNames(
    'socket-renderer',
    `socket-${type}`,
    `socket-${socket.dataType}`,
    {
      'socket-connected': connected,
      'socket-hovered': hovered,
      'socket-execution': socket.isExecution,
      'socket-optional': socket.optional
    },
    className
  )
  
  return (
    <div
      className={socketClassName}
      style={{
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        justifyContent: type === 'input' ? 'flex-start' : 'flex-end'
      }}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 插槽图形 */}
      <div
        className="socket-graphic"
        style={getSocketStyle()}
        title={`${socket.name} (${socket.dataType})`}
      />
      
      {/* 悬停提示 */}
      {hovered && (
        <div className={`socket-tooltip ${type === 'input' ? 'tooltip-right' : 'tooltip-left'}`}>
          <div className="tooltip-content">
            <div className="tooltip-name">{socket.name}</div>
            <div className="tooltip-type">{socket.dataType}</div>
            {socket.description && (
              <div className="tooltip-description">{socket.description}</div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default SocketRenderer
