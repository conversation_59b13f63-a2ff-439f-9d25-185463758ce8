/**
 * DL-Engine 开关组件
 * 
 * 基于 Ant Design Switch 的增强版本
 */

import React from 'react'
import { Switch, SwitchProps } from 'antd'
import classNames from 'classnames'
import DLIcon from './DLIcon'

/**
 * DL开关属性
 */
export interface DLSwitchProps extends SwitchProps {
  /** 开关标签 */
  label?: string
  /** 开关描述 */
  description?: string
  /** 开启时的图标 */
  checkedIcon?: string | React.ReactNode
  /** 关闭时的图标 */
  uncheckedIcon?: string | React.ReactNode
  /** 开启时的文本 */
  checkedText?: string
  /** 关闭时的文本 */
  uncheckedText?: string
  /** 自定义类名 */
  className?: string
}

/**
 * DL开关组件
 */
const DLSwitch: React.FC<DLSwitchProps> = ({
  label,
  description,
  checkedIcon,
  uncheckedIcon,
  checkedText,
  uncheckedText,
  className,
  ...props
}) => {
  /**
   * 渲染图标
   */
  const renderIcon = (icon: string | React.ReactNode | undefined) => {
    if (!icon) return undefined
    
    if (typeof icon === 'string') {
      return <DLIcon name={icon} />
    }
    
    return icon
  }
  
  /**
   * 开关类名
   */
  const switchClassName = classNames(
    'dl-switch',
    {
      'dl-switch--with-label': label
    },
    className
  )
  
  return (
    <div className={switchClassName}>
      <div className="flex items-center">
        {/* 开关 */}
        <Switch
          {...props}
          checkedChildren={checkedIcon ? renderIcon(checkedIcon) : checkedText}
          unCheckedChildren={uncheckedIcon ? renderIcon(uncheckedIcon) : uncheckedText}
        />
        
        {/* 标签和描述 */}
        {(label || description) && (
          <div className="ml-3">
            {label && (
              <div className="text-sm text-gray-900">{label}</div>
            )}
            {description && (
              <div className="text-xs text-gray-500">{description}</div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default DLSwitch
