/**
 * DL-Engine 表单项组件
 * 
 * 动态表单字段渲染器
 */

import React from 'react'
import { Form, FormInstance, Input, InputNumber, Select, Switch, Slider, DatePicker, Upload, Button } from 'antd'
import { UploadOutlined } from '@ant-design/icons'
import DLColorPicker from '../basic/DLColorPicker'
import { DLFormFieldConfig } from './DLForm'

/**
 * 表单项属性
 */
export interface DLFormItemProps {
  /** 字段配置 */
  field: DLFormFieldConfig
  /** 表单实例 */
  form: FormInstance
}

/**
 * DL表单项组件
 */
const DLFormItem: React.FC<DLFormItemProps> = ({ field, form }) => {
  /**
   * 渲染字段控件
   */
  const renderControl = () => {
    const { type, options, placeholder, props = {} } = field
    
    switch (type) {
      case 'input':
        return (
          <Input
            placeholder={placeholder}
            {...props}
          />
        )
      
      case 'textarea':
        return (
          <Input.TextArea
            placeholder={placeholder}
            rows={4}
            {...props}
          />
        )
      
      case 'number':
        return (
          <InputNumber
            placeholder={placeholder}
            style={{ width: '100%' }}
            {...props}
          />
        )
      
      case 'select':
        return (
          <Select
            placeholder={placeholder}
            options={options}
            {...props}
          />
        )
      
      case 'switch':
        return (
          <Switch
            {...props}
          />
        )
      
      case 'slider':
        return (
          <Slider
            {...props}
          />
        )
      
      case 'color':
        return (
          <DLColorPicker
            {...props}
          />
        )
      
      case 'date':
        return (
          <DatePicker
            placeholder={placeholder}
            style={{ width: '100%' }}
            {...props}
          />
        )
      
      case 'upload':
        return (
          <Upload
            {...props}
          >
            <Button icon={<UploadOutlined />}>
              {placeholder || '选择文件'}
            </Button>
          </Upload>
        )
      
      case 'custom':
        if (field.render) {
          return (
            <Form.Item noStyle shouldUpdate>
              {({ getFieldValue, setFieldValue }) => {
                const value = getFieldValue(field.name)
                const onChange = (newValue: any) => {
                  setFieldValue(field.name, newValue)
                }
                return field.render!(value, onChange, field)
              }}
            </Form.Item>
          )
        }
        return null
      
      default:
        return (
          <Input
            placeholder={placeholder}
            {...props}
          />
        )
    }
  }
  
  /**
   * 构建验证规则
   */
  const buildRules = () => {
    const rules = [...(field.rules || [])]
    
    if (field.required) {
      rules.unshift({
        required: true,
        message: `请输入${field.label}`
      })
    }
    
    return rules
  }
  
  return (
    <Form.Item
      name={field.name}
      label={field.label}
      rules={buildRules()}
      initialValue={field.defaultValue}
      dependencies={field.dependencies}
      help={field.description}
    >
      {renderControl()}
    </Form.Item>
  )
}

export default DLFormItem
