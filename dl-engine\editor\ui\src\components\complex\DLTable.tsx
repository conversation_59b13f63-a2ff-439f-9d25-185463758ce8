/**
 * DL-Engine 表格组件
 * 
 * 基于 Ant Design Table 的增强版本，专为编辑器场景优化
 */

import React, { useState, useCallback, useMemo } from 'react'
import { Table, TableProps, Button, Input, Space, Dropdown, MenuProps } from 'antd'
import { useTranslation } from 'react-i18next'
import { SearchOutlined, FilterOutlined, MoreOutlined, ReloadOutlined } from '@ant-design/icons'
import classNames from 'classnames'
import DLIcon from '../basic/DLIcon'
import DLTooltip from '../basic/DLTooltip'

/**
 * 表格列配置扩展
 */
export interface DLColumnType<T = any> extends Omit<TableProps<T>['columns'][0], 'render'> {
  /** 是否可搜索 */
  searchable?: boolean
  /** 是否可筛选 */
  filterable?: boolean
  /** 是否可排序 */
  sortable?: boolean
  /** 列类型 */
  columnType?: 'text' | 'number' | 'date' | 'boolean' | 'action' | 'custom'
  /** 自定义渲染函数 */
  render?: (value: any, record: T, index: number) => React.ReactNode
  /** 编辑器类型（用于可编辑表格） */
  editorType?: 'input' | 'select' | 'number' | 'date' | 'switch'
  /** 编辑器选项 */
  editorOptions?: any
}

/**
 * DL表格属性
 */
export interface DLTableProps<T = any> extends Omit<TableProps<T>, 'columns'> {
  /** 表格列配置 */
  columns: DLColumnType<T>[]
  /** 是否显示工具栏 */
  showToolbar?: boolean
  /** 是否显示搜索框 */
  showSearch?: boolean
  /** 是否显示刷新按钮 */
  showRefresh?: boolean
  /** 是否显示列设置 */
  showColumnSettings?: boolean
  /** 是否可编辑 */
  editable?: boolean
  /** 搜索占位符 */
  searchPlaceholder?: string
  /** 工具栏额外操作 */
  toolbarActions?: React.ReactNode
  /** 刷新回调 */
  onRefresh?: () => void
  /** 搜索回调 */
  onSearch?: (value: string) => void
  /** 行编辑回调 */
  onRowEdit?: (record: T, field: string, value: any) => void
  /** 自定义类名 */
  className?: string
}

/**
 * DL表格组件
 */
const DLTable = <T extends Record<string, any> = any>({
  columns,
  showToolbar = true,
  showSearch = true,
  showRefresh = true,
  showColumnSettings = true,
  editable = false,
  searchPlaceholder,
  toolbarActions,
  onRefresh,
  onSearch,
  onRowEdit,
  className,
  ...props
}: DLTableProps<T>) => {
  const { t } = useTranslation()
  const [searchValue, setSearchValue] = useState('')
  const [editingKey, setEditingKey] = useState<string>('')
  
  /**
   * 处理搜索
   */
  const handleSearch = useCallback((value: string) => {
    setSearchValue(value)
    onSearch?.(value)
  }, [onSearch])
  
  /**
   * 处理刷新
   */
  const handleRefresh = useCallback(() => {
    onRefresh?.()
  }, [onRefresh])
  
  /**
   * 处理行编辑
   */
  const handleRowEdit = useCallback((record: T, field: string, value: any) => {
    onRowEdit?.(record, field, value)
  }, [onRowEdit])
  
  /**
   * 列设置菜单
   */
  const columnSettingsMenu: MenuProps = useMemo(() => ({
    items: columns.map((col, index) => ({
      key: col.key || col.dataIndex || index,
      label: col.title,
      // TODO: 实现列显示/隐藏功能
    }))
  }), [columns])
  
  /**
   * 处理后的列配置
   */
  const processedColumns = useMemo(() => {
    return columns.map((col) => {
      const processedCol = { ...col }
      
      // 添加搜索功能
      if (col.searchable) {
        processedCol.filterDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
          <div style={{ padding: 8 }}>
            <Input
              placeholder={`搜索 ${col.title}`}
              value={selectedKeys[0]}
              onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
              onPressEnter={() => confirm()}
              style={{ marginBottom: 8, display: 'block' }}
            />
            <Space>
              <Button
                type="primary"
                onClick={() => confirm()}
                icon={<SearchOutlined />}
                size="small"
                style={{ width: 90 }}
              >
                搜索
              </Button>
              <Button
                onClick={() => clearFilters?.()}
                size="small"
                style={{ width: 90 }}
              >
                重置
              </Button>
            </Space>
          </div>
        )
        processedCol.filterIcon = (filtered: boolean) => (
          <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
        )
      }
      
      // 添加排序功能
      if (col.sortable) {
        processedCol.sorter = true
      }
      
      return processedCol
    })
  }, [columns])
  
  /**
   * 表格类名
   */
  const tableClassName = classNames(
    'dl-table',
    {
      'dl-table--editable': editable,
      'dl-table--with-toolbar': showToolbar
    },
    className
  )
  
  return (
    <div className={tableClassName}>
      {/* 工具栏 */}
      {showToolbar && (
        <div className="dl-table-toolbar">
          <div className="dl-table-toolbar-left">
            {showSearch && (
              <Input.Search
                placeholder={searchPlaceholder || t('common.search')}
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                onSearch={handleSearch}
                style={{ width: 200 }}
                allowClear
              />
            )}
          </div>
          
          <div className="dl-table-toolbar-right">
            <Space>
              {toolbarActions}
              
              {showRefresh && (
                <DLTooltip title={t('common.refresh')}>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleRefresh}
                  />
                </DLTooltip>
              )}
              
              {showColumnSettings && (
                <Dropdown menu={columnSettingsMenu} trigger={['click']}>
                  <Button icon={<FilterOutlined />} />
                </Dropdown>
              )}
              
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'export',
                      label: '导出数据',
                      icon: <DLIcon name="download" />
                    },
                    {
                      key: 'print',
                      label: '打印表格',
                      icon: <DLIcon name="printer" />
                    }
                  ]
                }}
                trigger={['click']}
              >
                <Button icon={<MoreOutlined />} />
              </Dropdown>
            </Space>
          </div>
        </div>
      )}
      
      {/* 表格主体 */}
      <Table<T>
        {...props}
        columns={processedColumns}
        className="dl-table-main"
        scroll={{ x: 'max-content' }}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          ...props.pagination
        }}
      />
    </div>
  )
}

export default DLTable
