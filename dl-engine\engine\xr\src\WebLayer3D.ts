/**
 * 3D Web层渲染器
 * 
 * 将2D Web内容渲染到3D空间中
 */

import * as THREE from 'three'

/**
 * Web层配置
 */
export interface WebLayer3DConfig {
  /** 层宽度（米） */
  width: number
  /** 层高度（米） */
  height: number
  /** 像素密度 */
  pixelDensity: number
  /** 是否启用交互 */
  interactive: boolean
  /** 是否双面渲染 */
  doubleSided: boolean
  /** 透明度 */
  opacity: number
  /** 是否启用抗锯齿 */
  antialias: boolean
}

/**
 * 交互事件类型
 */
export interface WebLayer3DInteraction {
  type: 'click' | 'hover' | 'focus' | 'blur'
  position: { x: number; y: number }
  uv: { u: number; v: number }
  worldPosition: THREE.Vector3
  target: HTMLElement | null
}

/**
 * 3D Web层类
 */
export class WebLayer3D extends THREE.Object3D {
  private config: WebLayer3DConfig
  private domElement: HTMLElement
  private canvas: HTMLCanvasElement
  private context: CanvasRenderingContext2D
  private texture: THREE.CanvasTexture
  private material: THREE.MeshBasicMaterial
  private geometry: THREE.PlaneGeometry
  private mesh: THREE.Mesh
  private raycaster: THREE.Raycaster
  private mouse: THREE.Vector2
  private isHovered: boolean = false
  private lastHoveredElement: HTMLElement | null = null
  
  constructor(domElement: HTMLElement, config: Partial<WebLayer3DConfig> = {}) {
    super()
    
    this.config = {
      width: 1,
      height: 0.75,
      pixelDensity: 1,
      interactive: true,
      doubleSided: false,
      opacity: 1,
      antialias: true,
      ...config
    }
    
    this.domElement = domElement
    this.raycaster = new THREE.Raycaster()
    this.mouse = new THREE.Vector2()
    
    this.initializeCanvas()
    this.initializeMesh()
    this.setupInteraction()
    this.startRenderLoop()
  }
  
  /**
   * 初始化画布
   */
  private initializeCanvas(): void {
    const pixelWidth = this.config.width * 1000 * this.config.pixelDensity
    const pixelHeight = this.config.height * 1000 * this.config.pixelDensity
    
    this.canvas = document.createElement('canvas')
    this.canvas.width = pixelWidth
    this.canvas.height = pixelHeight
    
    this.context = this.canvas.getContext('2d', {
      antialias: this.config.antialias
    })!
    
    // 设置高DPI支持
    this.context.scale(this.config.pixelDensity, this.config.pixelDensity)
  }
  
  /**
   * 初始化3D网格
   */
  private initializeMesh(): void {
    // 创建纹理
    this.texture = new THREE.CanvasTexture(this.canvas)
    this.texture.minFilter = THREE.LinearFilter
    this.texture.magFilter = THREE.LinearFilter
    this.texture.format = THREE.RGBAFormat
    
    // 创建材质
    this.material = new THREE.MeshBasicMaterial({
      map: this.texture,
      transparent: true,
      opacity: this.config.opacity,
      side: this.config.doubleSided ? THREE.DoubleSide : THREE.FrontSide
    })
    
    // 创建几何体
    this.geometry = new THREE.PlaneGeometry(this.config.width, this.config.height)
    
    // 创建网格
    this.mesh = new THREE.Mesh(this.geometry, this.material)
    this.add(this.mesh)
  }
  
  /**
   * 设置交互
   */
  private setupInteraction(): void {
    if (!this.config.interactive) return
    
    // 监听DOM元素的变化
    const observer = new MutationObserver(() => {
      this.needsUpdate = true
    })
    
    observer.observe(this.domElement, {
      childList: true,
      subtree: true,
      attributes: true,
      characterData: true
    })
    
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      this.needsUpdate = true
    })
  }
  
  /**
   * 渲染DOM到画布
   */
  private renderDOMToCanvas(): void {
    const rect = this.domElement.getBoundingClientRect()
    
    // 清除画布
    this.context.clearRect(0, 0, this.canvas.width / this.config.pixelDensity, this.canvas.height / this.config.pixelDensity)
    
    // 使用html2canvas或类似库渲染DOM
    // 这里简化实现，实际应该使用更完善的DOM渲染方案
    this.renderElementToCanvas(this.domElement, 0, 0)
    
    // 更新纹理
    this.texture.needsUpdate = true
  }
  
  /**
   * 渲染元素到画布（简化版本）
   */
  private renderElementToCanvas(element: HTMLElement, x: number, y: number): void {
    const style = window.getComputedStyle(element)
    const rect = element.getBoundingClientRect()
    
    // 渲染背景
    if (style.backgroundColor && style.backgroundColor !== 'rgba(0, 0, 0, 0)') {
      this.context.fillStyle = style.backgroundColor
      this.context.fillRect(x, y, rect.width, rect.height)
    }
    
    // 渲染文本
    if (element.textContent && element.children.length === 0) {
      this.context.fillStyle = style.color || '#000000'
      this.context.font = `${style.fontSize} ${style.fontFamily}`
      this.context.textAlign = 'left'
      this.context.textBaseline = 'top'
      this.context.fillText(element.textContent, x + 5, y + 5)
    }
    
    // 递归渲染子元素
    Array.from(element.children).forEach((child, index) => {
      if (child instanceof HTMLElement) {
        const childRect = child.getBoundingClientRect()
        const parentRect = element.getBoundingClientRect()
        this.renderElementToCanvas(
          child,
          x + (childRect.left - parentRect.left),
          y + (childRect.top - parentRect.top)
        )
      }
    })
  }
  
  /**
   * 处理射线投射交互
   */
  handleRaycast(camera: THREE.Camera, pointer: THREE.Vector2): WebLayer3DInteraction | null {
    if (!this.config.interactive) return null
    
    this.raycaster.setFromCamera(pointer, camera)
    const intersects = this.raycaster.intersectObject(this.mesh)
    
    if (intersects.length > 0) {
      const intersection = intersects[0]
      const uv = intersection.uv!
      
      // 转换UV坐标到像素坐标
      const pixelX = uv.x * (this.canvas.width / this.config.pixelDensity)
      const pixelY = (1 - uv.y) * (this.canvas.height / this.config.pixelDensity)
      
      // 查找对应的DOM元素
      const targetElement = this.getElementAtPosition(pixelX, pixelY)
      
      return {
        type: 'hover',
        position: { x: pixelX, y: pixelY },
        uv: { u: uv.x, v: uv.y },
        worldPosition: intersection.point,
        target: targetElement
      }
    }
    
    return null
  }
  
  /**
   * 获取指定位置的DOM元素
   */
  private getElementAtPosition(x: number, y: number): HTMLElement | null {
    // 这里需要实现从像素坐标到DOM元素的映射
    // 简化实现，实际应该维护一个元素位置映射
    return this.domElement
  }
  
  /**
   * 模拟鼠标事件
   */
  simulateMouseEvent(type: string, x: number, y: number): void {
    const element = this.getElementAtPosition(x, y)
    if (!element) return
    
    const event = new MouseEvent(type, {
      bubbles: true,
      cancelable: true,
      clientX: x,
      clientY: y
    })
    
    element.dispatchEvent(event)
  }
  
  /**
   * 更新层
   */
  update(): void {
    if (this.needsUpdate) {
      this.renderDOMToCanvas()
      this.needsUpdate = false
    }
  }
  
  /**
   * 开始渲染循环
   */
  private startRenderLoop(): void {
    const render = () => {
      this.update()
      requestAnimationFrame(render)
    }
    render()
  }
  
  /**
   * 设置位置
   */
  setPosition(x: number, y: number, z: number): void {
    this.position.set(x, y, z)
  }
  
  /**
   * 设置旋转
   */
  setRotation(x: number, y: number, z: number): void {
    this.rotation.set(x, y, z)
  }
  
  /**
   * 设置缩放
   */
  setScale(scale: number): void {
    this.scale.setScalar(scale)
  }
  
  /**
   * 设置透明度
   */
  setOpacity(opacity: number): void {
    this.config.opacity = opacity
    this.material.opacity = opacity
  }
  
  /**
   * 销毁层
   */
  dispose(): void {
    this.geometry.dispose()
    this.material.dispose()
    this.texture.dispose()
    this.remove(this.mesh)
  }
  
  private needsUpdate: boolean = true
}

export default WebLayer3D
