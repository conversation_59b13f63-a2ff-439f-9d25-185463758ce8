/**
 * XR UI容器组件
 * 
 * 在3D空间中创建可布局的UI容器
 */

import { Object3D, Vector3, Quaternion } from 'three'
import { WebLayer3D } from './WebLayer3D'

export interface XRUIContainerOptions {
  /** 容器宽度（米） */
  width?: number
  /** 容器高度（米） */
  height?: number
  /** 容器位置 */
  position?: Vector3
  /** 容器旋转 */
  rotation?: Quaternion
  /** 布局方向 */
  direction?: 'row' | 'column'
  /** 主轴对齐 */
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly'
  /** 交叉轴对齐 */
  alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch'
  /** 是否换行 */
  flexWrap?: 'nowrap' | 'wrap' | 'wrap-reverse'
  /** 间距 */
  gap?: number
  /** 内边距 */
  padding?: number
  /** 背景颜色 */
  backgroundColor?: string
  /** 边框颜色 */
  borderColor?: string
  /** 边框宽度 */
  borderWidth?: number
  /** 圆角半径 */
  borderRadius?: number
  /** 透明度 */
  opacity?: number
  /** 是否可见 */
  visible?: boolean
}

/**
 * XR UI容器类
 */
export class XRUIContainer extends Object3D {
  private options: Required<XRUIContainerOptions>
  private webLayer: WebLayer3D
  private containerElement: HTMLDivElement
  private childElements: HTMLElement[] = []

  constructor(options: XRUIContainerOptions = {}) {
    super()

    // 设置默认选项
    this.options = {
      width: 1.0,
      height: 0.6,
      position: new Vector3(0, 0, 0),
      rotation: new Quaternion(),
      direction: 'column',
      justifyContent: 'flex-start',
      alignItems: 'stretch',
      flexWrap: 'nowrap',
      gap: 8,
      padding: 16,
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      borderWidth: 0,
      borderRadius: 0,
      opacity: 1.0,
      visible: true,
      ...options
    }

    this.createContainer()
    this.updateTransform()
  }

  /**
   * 创建容器DOM结构
   */
  private createContainer(): void {
    // 创建容器元素
    this.containerElement = document.createElement('div')
    
    this.updateContainerStyle()

    // 创建WebLayer3D
    this.webLayer = new WebLayer3D(this.containerElement, {
      pixelWidth: 1024,
      pixelHeight: Math.round(1024 * (this.options.height / this.options.width))
    })

    this.webLayer.scale.set(this.options.width, this.options.height, 1)
    this.add(this.webLayer)
  }

  /**
   * 更新容器样式
   */
  private updateContainerStyle(): void {
    this.containerElement.style.cssText = `
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: ${this.options.direction};
      justify-content: ${this.options.justifyContent};
      align-items: ${this.options.alignItems};
      flex-wrap: ${this.options.flexWrap};
      gap: ${this.options.gap}px;
      padding: ${this.options.padding}px;
      background-color: ${this.options.backgroundColor};
      border: ${this.options.borderWidth}px solid ${this.options.borderColor};
      border-radius: ${this.options.borderRadius}px;
      opacity: ${this.options.opacity};
      box-sizing: border-box;
      overflow: hidden;
    `
  }

  /**
   * 更新变换
   */
  private updateTransform(): void {
    this.position.copy(this.options.position)
    this.quaternion.copy(this.options.rotation)
    this.visible = this.options.visible
  }

  /**
   * 添加子元素
   */
  addChild(element: HTMLElement): void {
    this.childElements.push(element)
    this.containerElement.appendChild(element)
  }

  /**
   * 移除子元素
   */
  removeChild(element: HTMLElement): void {
    const index = this.childElements.indexOf(element)
    if (index > -1) {
      this.childElements.splice(index, 1)
      this.containerElement.removeChild(element)
    }
  }

  /**
   * 清空所有子元素
   */
  clearChildren(): void {
    this.childElements.forEach(element => {
      this.containerElement.removeChild(element)
    })
    this.childElements = []
  }

  /**
   * 获取子元素数量
   */
  getChildCount(): number {
    return this.childElements.length
  }

  /**
   * 获取指定索引的子元素
   */
  getChildAt(index: number): HTMLElement | undefined {
    return this.childElements[index]
  }

  /**
   * 设置容器位置
   */
  setPosition(position: Vector3): void {
    this.options.position.copy(position)
    this.updateTransform()
  }

  /**
   * 设置容器旋转
   */
  setRotation(rotation: Quaternion): void {
    this.options.rotation.copy(rotation)
    this.updateTransform()
  }

  /**
   * 设置容器大小
   */
  setSize(width: number, height: number): void {
    this.options.width = width
    this.options.height = height
    this.webLayer.scale.set(width, height, 1)
  }

  /**
   * 设置布局方向
   */
  setDirection(direction: XRUIContainerOptions['direction']): void {
    this.options.direction = direction!
    this.updateContainerStyle()
  }

  /**
   * 设置主轴对齐
   */
  setJustifyContent(justifyContent: XRUIContainerOptions['justifyContent']): void {
    this.options.justifyContent = justifyContent!
    this.updateContainerStyle()
  }

  /**
   * 设置交叉轴对齐
   */
  setAlignItems(alignItems: XRUIContainerOptions['alignItems']): void {
    this.options.alignItems = alignItems!
    this.updateContainerStyle()
  }

  /**
   * 设置间距
   */
  setGap(gap: number): void {
    this.options.gap = gap
    this.updateContainerStyle()
  }

  /**
   * 设置内边距
   */
  setPadding(padding: number): void {
    this.options.padding = padding
    this.updateContainerStyle()
  }

  /**
   * 设置背景颜色
   */
  setBackgroundColor(backgroundColor: string): void {
    this.options.backgroundColor = backgroundColor
    this.updateContainerStyle()
  }

  /**
   * 设置边框
   */
  setBorder(width: number, color: string): void {
    this.options.borderWidth = width
    this.options.borderColor = color
    this.updateContainerStyle()
  }

  /**
   * 设置圆角
   */
  setBorderRadius(borderRadius: number): void {
    this.options.borderRadius = borderRadius
    this.updateContainerStyle()
  }

  /**
   * 设置透明度
   */
  setOpacity(opacity: number): void {
    this.options.opacity = Math.max(0, Math.min(1, opacity))
    this.updateContainerStyle()
  }

  /**
   * 设置可见性
   */
  setVisible(visible: boolean): void {
    this.options.visible = visible
    this.updateTransform()
  }

  /**
   * 显示容器
   */
  show(): void {
    this.setVisible(true)
  }

  /**
   * 隐藏容器
   */
  hide(): void {
    this.setVisible(false)
  }

  /**
   * 获取容器元素
   */
  getElement(): HTMLDivElement {
    return this.containerElement
  }

  /**
   * 获取所有子元素
   */
  getChildren(): HTMLElement[] {
    return [...this.childElements]
  }

  /**
   * 销毁容器
   */
  dispose(): void {
    this.clearChildren()
    if (this.webLayer) {
      this.webLayer.dispose()
    }
    if (this.parent) {
      this.parent.remove(this)
    }
  }
}

export default XRUIContainer
