/**
 * 连接管理器组件
 * 
 * 管理节点之间的连接逻辑
 */

import React, { useCallback, useMemo } from 'react'
import { ScriptGraph, NodeEditorState, Connection, NodeSocket, DataType } from '../types'

/**
 * 连接管理器属性
 */
export interface ConnectionManagerProps {
  /** 脚本图形 */
  graph: ScriptGraph
  /** 编辑器状态 */
  editorState: NodeEditorState
  /** 选中的连接 */
  selectedConnections: string[]
  /** 连接选择回调 */
  onConnectionSelect?: (connectionIds: string[]) => void
  /** 连接创建回调 */
  onConnectionCreate?: (connection: Partial<Connection>) => void
  /** 连接删除回调 */
  onConnectionDelete?: (connectionIds: string[]) => void
}

/**
 * 连接管理器组件
 */
const ConnectionManager: React.FC<ConnectionManagerProps> = ({
  graph,
  editorState,
  selectedConnections,
  onConnectionSelect,
  onConnectionCreate,
  onConnectionDelete
}) => {
  /**
   * 验证连接是否有效
   */
  const validateConnection = useCallback((
    sourceNodeId: string,
    sourceSocketId: string,
    targetNodeId: string,
    targetSocketId: string
  ): { valid: boolean; reason?: string } => {
    // 不能连接到自己
    if (sourceNodeId === targetNodeId) {
      return { valid: false, reason: '不能连接到自己' }
    }
    
    // 查找节点和插槽
    const sourceNode = graph.nodes.find(n => n.id === sourceNodeId)
    const targetNode = graph.nodes.find(n => n.id === targetNodeId)
    
    if (!sourceNode || !targetNode) {
      return { valid: false, reason: '节点不存在' }
    }
    
    const sourceSocket = sourceNode.definition.outputs?.find(s => s.id === sourceSocketId)
    const targetSocket = targetNode.definition.inputs?.find(s => s.id === targetSocketId)
    
    if (!sourceSocket || !targetSocket) {
      return { valid: false, reason: '插槽不存在' }
    }
    
    // 检查数据类型兼容性
    if (!isDataTypeCompatible(sourceSocket.dataType, targetSocket.dataType)) {
      return { valid: false, reason: '数据类型不兼容' }
    }
    
    // 检查是否已存在连接
    const existingConnection = graph.connections.find(c =>
      c.sourceNodeId === sourceNodeId &&
      c.sourceSocketId === sourceSocketId &&
      c.targetNodeId === targetNodeId &&
      c.targetSocketId === targetSocketId
    )
    
    if (existingConnection) {
      return { valid: false, reason: '连接已存在' }
    }
    
    // 检查目标插槽是否已有连接（非执行流）
    if (!targetSocket.isExecution && !targetSocket.allowMultiple) {
      const hasConnection = graph.connections.some(c =>
        c.targetNodeId === targetNodeId && c.targetSocketId === targetSocketId
      )
      
      if (hasConnection) {
        return { valid: false, reason: '目标插槽已有连接' }
      }
    }
    
    // 检查循环依赖
    if (wouldCreateCycle(sourceNodeId, targetNodeId, graph)) {
      return { valid: false, reason: '会创建循环依赖' }
    }
    
    return { valid: true }
  }, [graph])
  
  /**
   * 检查数据类型兼容性
   */
  const isDataTypeCompatible = (sourceType: DataType, targetType: DataType): boolean => {
    // ANY类型兼容所有类型
    if (sourceType === DataType.ANY || targetType === DataType.ANY) {
      return true
    }
    
    // 相同类型兼容
    if (sourceType === targetType) {
      return true
    }
    
    // 数字类型之间的兼容性
    const numericTypes = [DataType.NUMBER, DataType.VECTOR2, DataType.VECTOR3]
    if (numericTypes.includes(sourceType) && numericTypes.includes(targetType)) {
      return true
    }
    
    return false
  }
  
  /**
   * 检查是否会创建循环依赖
   */
  const wouldCreateCycle = (sourceNodeId: string, targetNodeId: string, graph: ScriptGraph): boolean => {
    const visited = new Set<string>()
    const recursionStack = new Set<string>()
    
    const hasCycle = (nodeId: string): boolean => {
      if (recursionStack.has(nodeId)) {
        return true
      }
      
      if (visited.has(nodeId)) {
        return false
      }
      
      visited.add(nodeId)
      recursionStack.add(nodeId)
      
      // 查找所有从当前节点出发的连接
      const outgoingConnections = graph.connections.filter(c => c.sourceNodeId === nodeId)
      
      // 添加新连接到检查中
      if (nodeId === sourceNodeId) {
        outgoingConnections.push({
          id: 'temp',
          sourceNodeId,
          sourceSocketId: '',
          targetNodeId,
          targetSocketId: ''
        })
      }
      
      for (const connection of outgoingConnections) {
        if (hasCycle(connection.targetNodeId)) {
          return true
        }
      }
      
      recursionStack.delete(nodeId)
      return false
    }
    
    return hasCycle(sourceNodeId)
  }
  
  /**
   * 创建连接
   */
  const createConnection = useCallback((
    sourceNodeId: string,
    sourceSocketId: string,
    targetNodeId: string,
    targetSocketId: string
  ) => {
    const validation = validateConnection(sourceNodeId, sourceSocketId, targetNodeId, targetSocketId)
    
    if (!validation.valid) {
      console.warn('连接无效:', validation.reason)
      return false
    }
    
    const connection: Partial<Connection> = {
      id: `${sourceNodeId}_${sourceSocketId}_${targetNodeId}_${targetSocketId}`,
      sourceNodeId,
      sourceSocketId,
      targetNodeId,
      targetSocketId
    }
    
    onConnectionCreate?.(connection)
    return true
  }, [validateConnection, onConnectionCreate])
  
  /**
   * 删除连接
   */
  const deleteConnection = useCallback((connectionId: string) => {
    onConnectionDelete?.([connectionId])
  }, [onConnectionDelete])
  
  /**
   * 删除选中的连接
   */
  const deleteSelectedConnections = useCallback(() => {
    if (selectedConnections.length > 0) {
      onConnectionDelete?.(selectedConnections)
    }
  }, [selectedConnections, onConnectionDelete])
  
  /**
   * 获取连接统计信息
   */
  const connectionStats = useMemo(() => {
    const total = graph.connections.length
    const executionConnections = graph.connections.filter(c => {
      const sourceNode = graph.nodes.find(n => n.id === c.sourceNodeId)
      const sourceSocket = sourceNode?.definition.outputs?.find(s => s.id === c.sourceSocketId)
      return sourceSocket?.isExecution
    }).length
    const dataConnections = total - executionConnections
    
    return {
      total,
      execution: executionConnections,
      data: dataConnections
    }
  }, [graph])
  
  // 连接管理器是一个逻辑组件，不渲染UI
  return null
}

export default ConnectionManager
