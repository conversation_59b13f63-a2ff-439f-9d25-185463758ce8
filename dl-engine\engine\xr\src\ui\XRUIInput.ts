/**
 * XR UI输入框组件
 * 
 * 在3D空间中创建可交互的输入框
 */

import { Object3D, Vector3 } from 'three'
import { WebLayer3D } from './WebLayer3D'

export interface XRUIInputOptions {
  /** 输入框宽度（米） */
  width?: number
  /** 输入框高度（米） */
  height?: number
  /** 输入框位置 */
  position?: Vector3
  /** 占位符文本 */
  placeholder?: string
  /** 初始值 */
  value?: string
  /** 输入类型 */
  type?: 'text' | 'password' | 'number' | 'email'
  /** 最大长度 */
  maxLength?: number
  /** 是否只读 */
  readonly?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 背景颜色 */
  backgroundColor?: string
  /** 文本颜色 */
  textColor?: string
  /** 边框颜色 */
  borderColor?: string
  /** 聚焦边框颜色 */
  focusBorderColor?: string
  /** 字体大小 */
  fontSize?: number
  /** 值变化回调 */
  onChange?: (value: string) => void
  /** 聚焦回调 */
  onFocus?: () => void
  /** 失焦回调 */
  onBlur?: () => void
  /** 回车回调 */
  onEnter?: (value: string) => void
}

/**
 * XR UI输入框类
 */
export class XRUIInput extends Object3D {
  private options: Required<XRUIInputOptions>
  private webLayer: WebLayer3D
  private containerElement: HTMLDivElement
  private inputElement: HTMLInputElement
  private isFocused = false

  constructor(options: XRUIInputOptions = {}) {
    super()

    // 设置默认选项
    this.options = {
      width: 0.4,
      height: 0.08,
      position: new Vector3(0, 0, 0),
      placeholder: '请输入...',
      value: '',
      type: 'text',
      maxLength: 100,
      readonly: false,
      disabled: false,
      backgroundColor: '#2d2d30',
      textColor: '#ffffff',
      borderColor: '#3c3c3c',
      focusBorderColor: '#0078d4',
      fontSize: 14,
      onChange: () => {},
      onFocus: () => {},
      onBlur: () => {},
      onEnter: () => {},
      ...options
    }

    this.createInput()
    this.setupInteraction()
    this.updateTransform()
  }

  /**
   * 创建输入框DOM结构
   */
  private createInput(): void {
    // 创建容器元素
    this.containerElement = document.createElement('div')
    this.containerElement.style.cssText = `
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 12px;
      background-color: ${this.options.backgroundColor};
      border: 2px solid ${this.options.borderColor};
      border-radius: 6px;
      transition: border-color 0.2s ease;
    `

    // 创建输入框元素
    this.inputElement = document.createElement('input')
    this.inputElement.type = this.options.type
    this.inputElement.placeholder = this.options.placeholder
    this.inputElement.value = this.options.value
    this.inputElement.maxLength = this.options.maxLength
    this.inputElement.readOnly = this.options.readonly
    this.inputElement.disabled = this.options.disabled

    this.updateInputStyle()

    // 组装元素
    this.containerElement.appendChild(this.inputElement)

    // 创建WebLayer3D
    this.webLayer = new WebLayer3D(this.containerElement, {
      pixelWidth: 512,
      pixelHeight: Math.round(512 * (this.options.height / this.options.width))
    })

    this.webLayer.scale.set(this.options.width, this.options.height, 1)
    this.add(this.webLayer)
  }

  /**
   * 更新输入框样式
   */
  private updateInputStyle(): void {
    this.inputElement.style.cssText = `
      width: 100%;
      height: 100%;
      background: transparent;
      border: none;
      outline: none;
      color: ${this.options.textColor};
      font-size: ${this.options.fontSize}px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      padding: 0;
      margin: 0;
    `

    // 更新容器边框颜色
    const borderColor = this.isFocused ? this.options.focusBorderColor : this.options.borderColor
    this.containerElement.style.borderColor = borderColor

    // 设置占位符样式
    const style = document.createElement('style')
    style.textContent = `
      .xr-input::placeholder {
        color: #888888;
        opacity: 1;
      }
    `
    if (!document.head.contains(style)) {
      document.head.appendChild(style)
    }
    this.inputElement.className = 'xr-input'
  }

  /**
   * 设置交互功能
   */
  private setupInteraction(): void {
    // 输入事件
    this.inputElement.addEventListener('input', this.onInput.bind(this))
    this.inputElement.addEventListener('focus', this.onFocus.bind(this))
    this.inputElement.addEventListener('blur', this.onBlur.bind(this))
    this.inputElement.addEventListener('keydown', this.onKeyDown.bind(this))

    // 容器点击事件
    this.containerElement.addEventListener('click', this.onContainerClick.bind(this))
  }

  /**
   * 输入事件处理
   */
  private onInput(event: Event): void {
    const target = event.target as HTMLInputElement
    this.options.onChange(target.value)
  }

  /**
   * 聚焦事件处理
   */
  private onFocus(event: Event): void {
    this.isFocused = true
    this.updateInputStyle()
    this.options.onFocus()
  }

  /**
   * 失焦事件处理
   */
  private onBlur(event: Event): void {
    this.isFocused = false
    this.updateInputStyle()
    this.options.onBlur()
  }

  /**
   * 键盘事件处理
   */
  private onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.options.onEnter(this.inputElement.value)
    }
  }

  /**
   * 容器点击事件处理
   */
  private onContainerClick(event: Event): void {
    if (!this.options.disabled && !this.options.readonly) {
      this.inputElement.focus()
    }
  }

  /**
   * 更新变换
   */
  private updateTransform(): void {
    this.position.copy(this.options.position)
  }

  /**
   * 设置输入框值
   */
  setValue(value: string): void {
    this.options.value = value
    this.inputElement.value = value
  }

  /**
   * 获取输入框值
   */
  getValue(): string {
    return this.inputElement.value
  }

  /**
   * 设置占位符
   */
  setPlaceholder(placeholder: string): void {
    this.options.placeholder = placeholder
    this.inputElement.placeholder = placeholder
  }

  /**
   * 设置位置
   */
  setPosition(position: Vector3): void {
    this.options.position.copy(position)
    this.updateTransform()
  }

  /**
   * 设置大小
   */
  setSize(width: number, height: number): void {
    this.options.width = width
    this.options.height = height
    this.webLayer.scale.set(width, height, 1)
  }

  /**
   * 设置禁用状态
   */
  setDisabled(disabled: boolean): void {
    this.options.disabled = disabled
    this.inputElement.disabled = disabled
  }

  /**
   * 设置只读状态
   */
  setReadonly(readonly: boolean): void {
    this.options.readonly = readonly
    this.inputElement.readOnly = readonly
  }

  /**
   * 聚焦输入框
   */
  focus(): void {
    this.inputElement.focus()
  }

  /**
   * 失焦输入框
   */
  blur(): void {
    this.inputElement.blur()
  }

  /**
   * 选中所有文本
   */
  selectAll(): void {
    this.inputElement.select()
  }

  /**
   * 获取输入框元素
   */
  getElement(): HTMLInputElement {
    return this.inputElement
  }

  /**
   * 销毁输入框
   */
  dispose(): void {
    if (this.webLayer) {
      this.webLayer.dispose()
    }
    if (this.parent) {
      this.parent.remove(this)
    }
  }
}

export default XRUIInput
