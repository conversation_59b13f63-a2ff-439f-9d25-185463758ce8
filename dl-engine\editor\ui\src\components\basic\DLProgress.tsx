/**
 * DL-Engine 进度条组件
 * 
 * 基于 Ant Design Progress 的增强版本
 */

import React, { useMemo } from 'react'
import { Progress, ProgressProps } from 'antd'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'
import DLIcon from './DLIcon'

/**
 * 进度条状态
 */
export type ProgressStatus = 'normal' | 'success' | 'exception' | 'active'

/**
 * DL进度条属性
 */
export interface DLProgressProps extends Omit<ProgressProps, 'status' | 'format'> {
  /** 进度条状态 */
  status?: ProgressStatus
  /** 是否显示百分比文本 */
  showPercent?: boolean
  /** 是否显示剩余时间 */
  showTimeRemaining?: boolean
  /** 预估总时间（毫秒） */
  estimatedTime?: number
  /** 已用时间（毫秒） */
  elapsedTime?: number
  /** 自定义格式化函数 */
  format?: (percent?: number, successPercent?: number) => React.ReactNode
  /** 进度条标题 */
  title?: string
  /** 进度条描述 */
  description?: string
  /** 是否显示动画 */
  animated?: boolean
  /** 自定义颜色 */
  color?: string | { from: string; to: string }
  /** 是否显示状态图标 */
  showStatusIcon?: boolean
  /** 自定义类名 */
  className?: string
}

/**
 * DL进度条组件
 */
const DLProgress: React.FC<DLProgressProps> = ({
  status = 'normal',
  showPercent = true,
  showTimeRemaining = false,
  estimatedTime,
  elapsedTime,
  format,
  title,
  description,
  animated = true,
  color,
  showStatusIcon = true,
  className,
  percent = 0,
  ...props
}) => {
  const { t } = useTranslation()

  /**
   * 计算剩余时间
   */
  const timeRemaining = useMemo(() => {
    if (!showTimeRemaining || !estimatedTime || !elapsedTime || percent === 0) {
      return null
    }

    const remaining = Math.max(0, estimatedTime - elapsedTime)
    const minutes = Math.floor(remaining / 60000)
    const seconds = Math.floor((remaining % 60000) / 1000)

    if (minutes > 0) {
      return `${minutes}分${seconds}秒`
    }
    return `${seconds}秒`
  }, [showTimeRemaining, estimatedTime, elapsedTime, percent])

  /**
   * 获取状态图标
   */
  const getStatusIcon = () => {
    if (!showStatusIcon) return null

    switch (status) {
      case 'success':
        return <DLIcon name="check-circle" style={{ color: '#52c41a' }} />
      case 'exception':
        return <DLIcon name="close-circle" style={{ color: '#ff4d4f' }} />
      case 'active':
        return <DLIcon name="loading" spin style={{ color: '#1890ff' }} />
      default:
        return null
    }
  }

  /**
   * 自定义格式化函数
   */
  const customFormat = (percent?: number, successPercent?: number) => {
    if (format) {
      return format(percent, successPercent)
    }

    if (!showPercent) {
      return null
    }

    const statusIcon = getStatusIcon()
    const percentText = `${Math.round(percent || 0)}%`

    if (statusIcon) {
      return (
        <span className="dl-progress-format">
          {statusIcon}
          <span className="dl-progress-percent">{percentText}</span>
        </span>
      )
    }

    return percentText
  }

  /**
   * 获取进度条颜色
   */
  const getStrokeColor = () => {
    if (color) {
      if (typeof color === 'string') {
        return color
      }
      return {
        '0%': color.from,
        '100%': color.to
      }
    }

    switch (status) {
      case 'success':
        return '#52c41a'
      case 'exception':
        return '#ff4d4f'
      case 'active':
        return '#1890ff'
      default:
        return undefined
    }
  }

  /**
   * 渲染进度条标题和描述
   */
  const renderHeader = () => {
    if (!title && !description && !timeRemaining) return null

    return (
      <div className="dl-progress-header">
        {title && <div className="dl-progress-title">{title}</div>}
        {description && <div className="dl-progress-description">{description}</div>}
        {timeRemaining && (
          <div className="dl-progress-time">
            {t('common.timeRemaining')}: {timeRemaining}
          </div>
        )}
      </div>
    )
  }

  const progressClassName = classNames(
    'dl-progress',
    {
      'dl-progress--animated': animated,
      'dl-progress--with-header': title || description || timeRemaining,
      [`dl-progress--${status}`]: status
    },
    className
  )

  return (
    <div className={progressClassName}>
      {renderHeader()}
      <Progress
        {...props}
        percent={percent}
        status={status === 'active' ? undefined : status}
        format={customFormat}
        strokeColor={getStrokeColor()}
        className="dl-progress-bar"
      />
    </div>
  )
}

// 添加样式
const progressStyles = `
.dl-progress {
  .dl-progress-header {
    margin-bottom: 8px;
  }

  .dl-progress-title {
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    margin-bottom: 4px;
  }

  .dl-progress-description {
    font-size: 12px;
    color: #888888;
    margin-bottom: 4px;
  }

  .dl-progress-time {
    font-size: 12px;
    color: #1890ff;
  }

  .dl-progress-format {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .dl-progress-percent {
    font-size: 12px;
  }

  &.dl-progress--animated {
    .ant-progress-bg {
      animation: dl-progress-active 2s ease-in-out infinite;
    }
  }

  &.dl-progress--success {
    .ant-progress-text {
      color: #52c41a;
    }
  }

  &.dl-progress--exception {
    .ant-progress-text {
      color: #ff4d4f;
    }
  }

  &.dl-progress--active {
    .ant-progress-text {
      color: #1890ff;
    }
  }
}

@keyframes dl-progress-active {
  0% {
    opacity: 0.3;
    transform: translateX(-100%);
  }
  50% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0.3;
    transform: translateX(100%);
  }
}
`

// 注入样式
if (typeof document !== 'undefined') {
  const styleId = 'dl-progress-styles'
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style')
    style.id = styleId
    style.textContent = progressStyles
    document.head.appendChild(style)
  }
}

export default DLProgress
