/**
 * DL-Engine 标签组件
 * 
 * 基于 Ant Design Tag 的增强版本
 */

import React from 'react'
import { Tag, TagProps } from 'antd'
import classNames from 'classnames'
import DLIcon from './DLIcon'

/**
 * 标签变体
 */
export type DLTagVariant = 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info'

/**
 * DL标签属性
 */
export interface DLTagProps extends Omit<TagProps, 'color'> {
  /** 标签变体 */
  variant?: DLTagVariant
  /** 标签图标 */
  icon?: string | React.ReactNode
  /** 是否可关闭 */
  closable?: boolean
  /** 是否可点击 */
  clickable?: boolean
  /** 点击回调 */
  onClick?: () => void
  /** 关闭回调 */
  onClose?: () => void
  /** 自定义类名 */
  className?: string
}

/**
 * DL标签组件
 */
const DLTag: React.FC<DLTagProps> = ({
  variant = 'default',
  icon,
  closable = false,
  clickable = false,
  onClick,
  onClose,
  className,
  children,
  ...props
}) => {
  /**
   * 获取标签颜色
   */
  const getTagColor = () => {
    switch (variant) {
      case 'primary':
        return 'blue'
      case 'success':
        return 'green'
      case 'warning':
        return 'orange'
      case 'error':
        return 'red'
      case 'info':
        return 'cyan'
      default:
        return 'default'
    }
  }
  
  /**
   * 渲染图标
   */
  const renderIcon = () => {
    if (!icon) return null
    
    if (typeof icon === 'string') {
      return <DLIcon name={icon} className="mr-1" />
    }
    
    return <span className="mr-1">{icon}</span>
  }
  
  /**
   * 标签类名
   */
  const tagClassName = classNames(
    'dl-tag',
    `dl-tag--${variant}`,
    {
      'dl-tag--clickable': clickable,
      'dl-tag--with-icon': icon
    },
    className
  )
  
  return (
    <Tag
      {...props}
      color={getTagColor()}
      closable={closable}
      onClose={onClose}
      onClick={clickable ? onClick : undefined}
      className={tagClassName}
      style={{
        cursor: clickable ? 'pointer' : 'default',
        ...props.style
      }}
    >
      {renderIcon()}
      {children}
    </Tag>
  )
}

export default DLTag
