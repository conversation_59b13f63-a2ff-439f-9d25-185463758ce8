/**
 * 插件API
 * 
 * 定义插件系统的核心接口和类型
 */

import { EventEmitter } from 'events'

/**
 * 插件类型
 */
export enum PluginType {
  EDITOR = 'editor',
  RENDERER = 'renderer',
  COMPONENT = 'component',
  TOOL = 'tool',
  EXTENSION = 'extension',
  THEME = 'theme',
  LANGUAGE = 'language'
}

/**
 * 插件状态
 */
export enum PluginStatus {
  INACTIVE = 'inactive',
  LOADING = 'loading',
  ACTIVE = 'active',
  ERROR = 'error',
  DISABLED = 'disabled'
}

/**
 * 插件权限
 */
export enum PluginPermission {
  READ_FILES = 'read_files',
  WRITE_FILES = 'write_files',
  NETWORK_ACCESS = 'network_access',
  SYSTEM_ACCESS = 'system_access',
  UI_MODIFICATION = 'ui_modification',
  EDITOR_CONTROL = 'editor_control'
}

/**
 * 插件元数据
 */
export interface PluginMetadata {
  /** 插件ID */
  id: string
  /** 插件名称 */
  name: string
  /** 插件版本 */
  version: string
  /** 插件描述 */
  description: string
  /** 插件作者 */
  author: string
  /** 插件类型 */
  type: PluginType
  /** 插件图标 */
  icon?: string
  /** 插件主页 */
  homepage?: string
  /** 插件仓库 */
  repository?: string
  /** 插件许可证 */
  license?: string
  /** 插件关键词 */
  keywords?: string[]
  /** 插件依赖 */
  dependencies?: Record<string, string>
  /** 引擎版本要求 */
  engineVersion?: string
  /** 插件权限 */
  permissions?: PluginPermission[]
  /** 插件配置架构 */
  configSchema?: any
  /** 插件入口文件 */
  main?: string
  /** 插件资源文件 */
  assets?: string[]
}

/**
 * 插件配置
 */
export interface PluginConfig {
  /** 是否启用 */
  enabled: boolean
  /** 自定义配置 */
  settings: Record<string, any>
  /** 快捷键绑定 */
  keybindings?: Record<string, string>
  /** UI配置 */
  ui?: {
    position?: string
    size?: { width: number; height: number }
    visible?: boolean
  }
}

/**
 * 插件上下文
 */
export interface PluginContext {
  /** 插件元数据 */
  metadata: PluginMetadata
  /** 插件配置 */
  config: PluginConfig
  /** 插件根目录 */
  rootPath: string
  /** 插件资源路径 */
  assetsPath: string
  /** 日志记录器 */
  logger: PluginLogger
  /** 事件发射器 */
  events: EventEmitter
}

/**
 * 插件日志记录器
 */
export interface PluginLogger {
  debug(message: string, ...args: any[]): void
  info(message: string, ...args: any[]): void
  warn(message: string, ...args: any[]): void
  error(message: string, ...args: any[]): void
}

/**
 * 插件API接口
 */
export interface IPluginAPI {
  /** 编辑器API */
  editor: EditorAPI
  /** UI API */
  ui: UIAPI
  /** 文件系统API */
  fs: FileSystemAPI
  /** 网络API */
  http: HttpAPI
  /** 存储API */
  storage: StorageAPI
  /** 命令API */
  commands: CommandAPI
  /** 菜单API */
  menus: MenuAPI
  /** 快捷键API */
  keybindings: KeybindingAPI
  /** 主题API */
  themes: ThemeAPI
  /** 国际化API */
  i18n: I18nAPI
}

/**
 * 编辑器API
 */
export interface EditorAPI {
  /** 获取当前场景 */
  getCurrentScene(): any
  /** 获取选中对象 */
  getSelectedObjects(): any[]
  /** 设置选中对象 */
  setSelectedObjects(objects: any[]): void
  /** 添加对象到场景 */
  addToScene(object: any): void
  /** 从场景移除对象 */
  removeFromScene(object: any): void
  /** 执行命令 */
  executeCommand(command: string, ...args: any[]): any
  /** 注册命令 */
  registerCommand(id: string, handler: Function): void
  /** 取消注册命令 */
  unregisterCommand(id: string): void
}

/**
 * UI API
 */
export interface UIAPI {
  /** 创建面板 */
  createPanel(id: string, options: PanelOptions): Panel
  /** 移除面板 */
  removePanel(id: string): void
  /** 显示通知 */
  showNotification(message: string, type?: 'info' | 'success' | 'warning' | 'error'): void
  /** 显示对话框 */
  showDialog(options: DialogOptions): Promise<any>
  /** 注册工具栏按钮 */
  registerToolbarButton(id: string, options: ToolbarButtonOptions): void
  /** 取消注册工具栏按钮 */
  unregisterToolbarButton(id: string): void
}

/**
 * 面板选项
 */
export interface PanelOptions {
  title: string
  content: React.ComponentType<any>
  position?: 'left' | 'right' | 'bottom' | 'center'
  size?: { width: number; height: number }
  resizable?: boolean
  closable?: boolean
}

/**
 * 面板接口
 */
export interface Panel {
  id: string
  show(): void
  hide(): void
  toggle(): void
  setTitle(title: string): void
  setContent(content: React.ComponentType<any>): void
  dispose(): void
}

/**
 * 对话框选项
 */
export interface DialogOptions {
  title: string
  content: string | React.ComponentType<any>
  buttons?: Array<{
    text: string
    type?: 'primary' | 'secondary' | 'danger'
    onClick?: () => void | Promise<void>
  }>
  width?: number
  height?: number
  modal?: boolean
}

/**
 * 工具栏按钮选项
 */
export interface ToolbarButtonOptions {
  icon: string
  tooltip: string
  onClick: () => void
  position?: 'left' | 'center' | 'right'
  group?: string
}

/**
 * 文件系统API
 */
export interface FileSystemAPI {
  /** 读取文件 */
  readFile(path: string): Promise<string>
  /** 写入文件 */
  writeFile(path: string, content: string): Promise<void>
  /** 检查文件是否存在 */
  exists(path: string): Promise<boolean>
  /** 创建目录 */
  mkdir(path: string): Promise<void>
  /** 列出目录内容 */
  readdir(path: string): Promise<string[]>
  /** 删除文件或目录 */
  remove(path: string): Promise<void>
  /** 监听文件变化 */
  watch(path: string, callback: (event: string, filename: string) => void): void
}

/**
 * HTTP API
 */
export interface HttpAPI {
  /** GET请求 */
  get(url: string, options?: RequestOptions): Promise<Response>
  /** POST请求 */
  post(url: string, data?: any, options?: RequestOptions): Promise<Response>
  /** PUT请求 */
  put(url: string, data?: any, options?: RequestOptions): Promise<Response>
  /** DELETE请求 */
  delete(url: string, options?: RequestOptions): Promise<Response>
}

/**
 * 请求选项
 */
export interface RequestOptions {
  headers?: Record<string, string>
  timeout?: number
  credentials?: 'include' | 'omit' | 'same-origin'
}

/**
 * 存储API
 */
export interface StorageAPI {
  /** 获取值 */
  get(key: string): Promise<any>
  /** 设置值 */
  set(key: string, value: any): Promise<void>
  /** 删除值 */
  remove(key: string): Promise<void>
  /** 清空存储 */
  clear(): Promise<void>
  /** 获取所有键 */
  keys(): Promise<string[]>
}

/**
 * 命令API
 */
export interface CommandAPI {
  /** 注册命令 */
  register(id: string, handler: CommandHandler): void
  /** 取消注册命令 */
  unregister(id: string): void
  /** 执行命令 */
  execute(id: string, ...args: any[]): Promise<any>
  /** 获取所有命令 */
  getAll(): Command[]
}

/**
 * 命令处理器
 */
export type CommandHandler = (...args: any[]) => any | Promise<any>

/**
 * 命令定义
 */
export interface Command {
  id: string
  title: string
  description?: string
  category?: string
  keybinding?: string
  handler: CommandHandler
}

/**
 * 菜单API
 */
export interface MenuAPI {
  /** 注册菜单项 */
  register(id: string, item: MenuItem): void
  /** 取消注册菜单项 */
  unregister(id: string): void
  /** 获取菜单项 */
  get(id: string): MenuItem | undefined
  /** 获取所有菜单项 */
  getAll(): MenuItem[]
}

/**
 * 菜单项
 */
export interface MenuItem {
  id: string
  label: string
  icon?: string
  command?: string
  submenu?: MenuItem[]
  separator?: boolean
  position?: number
  when?: string
}

/**
 * 快捷键API
 */
export interface KeybindingAPI {
  /** 注册快捷键 */
  register(keybinding: Keybinding): void
  /** 取消注册快捷键 */
  unregister(id: string): void
  /** 获取快捷键 */
  get(id: string): Keybinding | undefined
  /** 获取所有快捷键 */
  getAll(): Keybinding[]
}

/**
 * 快捷键定义
 */
export interface Keybinding {
  id: string
  key: string
  command: string
  when?: string
  args?: any[]
}

/**
 * 主题API
 */
export interface ThemeAPI {
  /** 注册主题 */
  register(theme: Theme): void
  /** 取消注册主题 */
  unregister(id: string): void
  /** 获取当前主题 */
  getCurrent(): Theme
  /** 设置当前主题 */
  setCurrent(id: string): void
  /** 获取所有主题 */
  getAll(): Theme[]
}

/**
 * 主题定义
 */
export interface Theme {
  id: string
  name: string
  type: 'light' | 'dark'
  colors: Record<string, string>
  styles?: Record<string, any>
}

/**
 * 国际化API
 */
export interface I18nAPI {
  /** 获取翻译 */
  t(key: string, params?: Record<string, any>): string
  /** 注册翻译 */
  register(locale: string, translations: Record<string, string>): void
  /** 获取当前语言 */
  getCurrentLocale(): string
  /** 设置当前语言 */
  setCurrentLocale(locale: string): void
  /** 获取支持的语言 */
  getSupportedLocales(): string[]
}

/**
 * 插件基类
 */
export abstract class Plugin {
  protected context: PluginContext
  protected api: IPluginAPI
  
  constructor(context: PluginContext, api: IPluginAPI) {
    this.context = context
    this.api = api
  }
  
  /** 插件激活时调用 */
  abstract activate(): void | Promise<void>
  
  /** 插件停用时调用 */
  abstract deactivate(): void | Promise<void>
  
  /** 获取插件元数据 */
  getMetadata(): PluginMetadata {
    return this.context.metadata
  }
  
  /** 获取插件配置 */
  getConfig(): PluginConfig {
    return this.context.config
  }
  
  /** 记录日志 */
  log(level: 'debug' | 'info' | 'warn' | 'error', message: string, ...args: any[]): void {
    this.context.logger[level](message, ...args)
  }
  
  /** 发射事件 */
  emit(event: string, ...args: any[]): void {
    this.context.events.emit(event, ...args)
  }
  
  /** 监听事件 */
  on(event: string, listener: (...args: any[]) => void): void {
    this.context.events.on(event, listener)
  }
  
  /** 取消监听事件 */
  off(event: string, listener: (...args: any[]) => void): void {
    this.context.events.off(event, listener)
  }
}

export default Plugin
