/**
 * DL-Engine 复选框组件
 * 
 * 基于 Ant Design Checkbox 的增强版本
 */

import React, { useCallback } from 'react'
import { Checkbox, CheckboxProps } from 'antd'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'
import DLTooltip from './DLTooltip'

/**
 * DL复选框属性
 */
export interface DLCheckboxProps extends Omit<CheckboxProps, 'onChange'> {
  /** 标签文本 */
  label?: string
  /** 工具提示 */
  tooltip?: string
  /** 工具提示位置 */
  tooltipPlacement?: 'top' | 'bottom' | 'left' | 'right'
  /** 是否显示标签 */
  showLabel?: boolean
  /** 标签位置 */
  labelPosition?: 'left' | 'right'
  /** 复选框大小 */
  size?: 'small' | 'default' | 'large'
  /** 复选框变体 */
  variant?: 'default' | 'button' | 'card'
  /** 值变化回调 */
  onChange?: (checked: boolean, event: React.ChangeEvent<HTMLInputElement>) => void
  /** 自定义类名 */
  className?: string
}

/**
 * DL复选框组件
 */
const DLCheckbox: React.FC<DLCheckboxProps> = ({
  label,
  tooltip,
  tooltipPlacement = 'top',
  showLabel = true,
  labelPosition = 'right',
  size = 'default',
  variant = 'default',
  onChange,
  className,
  children,
  ...props
}) => {
  const { t } = useTranslation()

  /**
   * 处理值变化
   */
  const handleChange = useCallback((e: any) => {
    onChange?.(e.target.checked, e)
  }, [onChange])

  /**
   * 渲染复选框内容
   */
  const renderCheckboxContent = () => {
    const displayLabel = label || children
    
    if (!showLabel || !displayLabel) {
      return null
    }

    return (
      <span className="dl-checkbox-label">
        {displayLabel}
      </span>
    )
  }

  /**
   * 复选框类名
   */
  const checkboxClassName = classNames(
    'dl-checkbox',
    {
      [`dl-checkbox--${size}`]: size !== 'default',
      [`dl-checkbox--${variant}`]: variant !== 'default',
      'dl-checkbox--label-left': labelPosition === 'left',
      'dl-checkbox--no-label': !showLabel || (!label && !children)
    },
    className
  )

  /**
   * 渲染复选框
   */
  const renderCheckbox = () => {
    if (variant === 'button') {
      return (
        <Checkbox.Button
          {...props}
          onChange={handleChange}
          className={checkboxClassName}
        >
          {renderCheckboxContent()}
        </Checkbox.Button>
      )
    }

    return (
      <Checkbox
        {...props}
        onChange={handleChange}
        className={checkboxClassName}
      >
        {renderCheckboxContent()}
      </Checkbox>
    )
  }

  // 如果有工具提示，包装在 Tooltip 中
  if (tooltip) {
    return (
      <DLTooltip title={tooltip} placement={tooltipPlacement}>
        {renderCheckbox()}
      </DLTooltip>
    )
  }

  return renderCheckbox()
}

// 复选框组
const DLCheckboxGroup: React.FC<{
  options: Array<{
    label: string
    value: any
    disabled?: boolean
  }>
  value?: any[]
  defaultValue?: any[]
  onChange?: (checkedValues: any[]) => void
  disabled?: boolean
  className?: string
}> = ({
  options,
  value,
  defaultValue,
  onChange,
  disabled = false,
  className
}) => {
  const groupClassName = classNames('dl-checkbox-group', className)

  return (
    <Checkbox.Group
      value={value}
      defaultValue={defaultValue}
      onChange={onChange}
      disabled={disabled}
      className={groupClassName}
    >
      {options.map(option => (
        <DLCheckbox
          key={option.value}
          value={option.value}
          disabled={option.disabled || disabled}
        >
          {option.label}
        </DLCheckbox>
      ))}
    </Checkbox.Group>
  )
}

// 添加样式
const checkboxStyles = `
.dl-checkbox {
  .ant-checkbox {
    border-radius: 4px;
  }

  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #1890ff;
    border-color: #1890ff;
  }

  .ant-checkbox:hover .ant-checkbox-inner {
    border-color: #40a9ff;
  }

  .dl-checkbox-label {
    margin-left: 8px;
    color: #ffffff;
    user-select: none;
  }

  &.dl-checkbox--small {
    .ant-checkbox {
      transform: scale(0.85);
    }
    
    .dl-checkbox-label {
      font-size: 12px;
    }
  }

  &.dl-checkbox--large {
    .ant-checkbox {
      transform: scale(1.15);
    }
    
    .dl-checkbox-label {
      font-size: 16px;
    }
  }

  &.dl-checkbox--label-left {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    
    .dl-checkbox-label {
      margin-left: 0;
      margin-right: 8px;
    }
  }

  &.dl-checkbox--button {
    .ant-checkbox-button-wrapper {
      background-color: #2d2d30;
      border-color: #3c3c3c;
      color: #ffffff;
    }

    .ant-checkbox-button-wrapper:hover {
      background-color: #3c3c3c;
      border-color: #1890ff;
      color: #1890ff;
    }

    .ant-checkbox-button-wrapper-checked {
      background-color: #1890ff;
      border-color: #1890ff;
      color: #ffffff;
    }
  }

  &.dl-checkbox--card {
    .ant-checkbox-wrapper {
      padding: 12px;
      border: 1px solid #3c3c3c;
      border-radius: 6px;
      background-color: #2d2d30;
      transition: all 0.2s ease;
    }

    .ant-checkbox-wrapper:hover {
      border-color: #1890ff;
      background-color: #3c3c3c;
    }

    .ant-checkbox-wrapper-checked {
      border-color: #1890ff;
      background-color: rgba(24, 144, 255, 0.1);
    }
  }

  &.dl-checkbox--no-label {
    .ant-checkbox-wrapper {
      display: inline-block;
    }
  }
}

.dl-checkbox-group {
  .ant-checkbox-group-item {
    margin-right: 16px;
    margin-bottom: 8px;
  }
}
`

// 注入样式
if (typeof document !== 'undefined') {
  const styleId = 'dl-checkbox-styles'
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style')
    style.id = styleId
    style.textContent = checkboxStyles
    document.head.appendChild(style)
  }
}

// 导出组件和组
DLCheckbox.Group = DLCheckboxGroup

export default DLCheckbox
