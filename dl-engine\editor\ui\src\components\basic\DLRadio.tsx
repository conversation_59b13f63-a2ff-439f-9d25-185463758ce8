/**
 * DL-Engine 单选框组件
 * 
 * 基于 Ant Design Radio 的增强版本
 */

import React, { useCallback } from 'react'
import { Radio, RadioProps } from 'antd'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'
import DLTooltip from './DLTooltip'

/**
 * DL单选框属性
 */
export interface DLRadioProps extends Omit<RadioProps, 'onChange'> {
  /** 标签文本 */
  label?: string
  /** 工具提示 */
  tooltip?: string
  /** 工具提示位置 */
  tooltipPlacement?: 'top' | 'bottom' | 'left' | 'right'
  /** 是否显示标签 */
  showLabel?: boolean
  /** 标签位置 */
  labelPosition?: 'left' | 'right'
  /** 单选框大小 */
  size?: 'small' | 'default' | 'large'
  /** 单选框变体 */
  variant?: 'default' | 'button' | 'card'
  /** 值变化回调 */
  onChange?: (checked: boolean, event: React.ChangeEvent<HTMLInputElement>) => void
  /** 自定义类名 */
  className?: string
}

/**
 * DL单选框组件
 */
const DLRadio: React.FC<DLRadioProps> = ({
  label,
  tooltip,
  tooltipPlacement = 'top',
  showLabel = true,
  labelPosition = 'right',
  size = 'default',
  variant = 'default',
  onChange,
  className,
  children,
  ...props
}) => {
  const { t } = useTranslation()

  /**
   * 处理值变化
   */
  const handleChange = useCallback((e: any) => {
    onChange?.(e.target.checked, e)
  }, [onChange])

  /**
   * 渲染单选框内容
   */
  const renderRadioContent = () => {
    const displayLabel = label || children
    
    if (!showLabel || !displayLabel) {
      return null
    }

    return (
      <span className="dl-radio-label">
        {displayLabel}
      </span>
    )
  }

  /**
   * 单选框类名
   */
  const radioClassName = classNames(
    'dl-radio',
    {
      [`dl-radio--${size}`]: size !== 'default',
      [`dl-radio--${variant}`]: variant !== 'default',
      'dl-radio--label-left': labelPosition === 'left',
      'dl-radio--no-label': !showLabel || (!label && !children)
    },
    className
  )

  /**
   * 渲染单选框
   */
  const renderRadio = () => {
    if (variant === 'button') {
      return (
        <Radio.Button
          {...props}
          onChange={handleChange}
          className={radioClassName}
        >
          {renderRadioContent()}
        </Radio.Button>
      )
    }

    return (
      <Radio
        {...props}
        onChange={handleChange}
        className={radioClassName}
      >
        {renderRadioContent()}
      </Radio>
    )
  }

  // 如果有工具提示，包装在 Tooltip 中
  if (tooltip) {
    return (
      <DLTooltip title={tooltip} placement={tooltipPlacement}>
        {renderRadio()}
      </DLTooltip>
    )
  }

  return renderRadio()
}

// 单选框组
const DLRadioGroup: React.FC<{
  options: Array<{
    label: string
    value: any
    disabled?: boolean
  }>
  value?: any
  defaultValue?: any
  onChange?: (value: any) => void
  disabled?: boolean
  direction?: 'horizontal' | 'vertical'
  size?: 'small' | 'default' | 'large'
  variant?: 'default' | 'button' | 'card'
  className?: string
}> = ({
  options,
  value,
  defaultValue,
  onChange,
  disabled = false,
  direction = 'horizontal',
  size = 'default',
  variant = 'default',
  className
}) => {
  const groupClassName = classNames(
    'dl-radio-group',
    {
      'dl-radio-group--vertical': direction === 'vertical',
      [`dl-radio-group--${variant}`]: variant !== 'default'
    },
    className
  )

  if (variant === 'button') {
    return (
      <Radio.Group
        value={value}
        defaultValue={defaultValue}
        onChange={onChange}
        disabled={disabled}
        className={groupClassName}
        size={size}
      >
        {options.map(option => (
          <Radio.Button
            key={option.value}
            value={option.value}
            disabled={option.disabled || disabled}
          >
            {option.label}
          </Radio.Button>
        ))}
      </Radio.Group>
    )
  }

  return (
    <Radio.Group
      value={value}
      defaultValue={defaultValue}
      onChange={onChange}
      disabled={disabled}
      className={groupClassName}
    >
      {options.map(option => (
        <DLRadio
          key={option.value}
          value={option.value}
          disabled={option.disabled || disabled}
          size={size}
          variant={variant}
        >
          {option.label}
        </DLRadio>
      ))}
    </Radio.Group>
  )
}

// 添加样式
const radioStyles = `
.dl-radio {
  .ant-radio {
    border-radius: 50%;
  }

  .ant-radio-checked .ant-radio-inner {
    background-color: #1890ff;
    border-color: #1890ff;
  }

  .ant-radio:hover .ant-radio-inner {
    border-color: #40a9ff;
  }

  .dl-radio-label {
    margin-left: 8px;
    color: #ffffff;
    user-select: none;
  }

  &.dl-radio--small {
    .ant-radio {
      transform: scale(0.85);
    }
    
    .dl-radio-label {
      font-size: 12px;
    }
  }

  &.dl-radio--large {
    .ant-radio {
      transform: scale(1.15);
    }
    
    .dl-radio-label {
      font-size: 16px;
    }
  }

  &.dl-radio--label-left {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    
    .dl-radio-label {
      margin-left: 0;
      margin-right: 8px;
    }
  }

  &.dl-radio--button {
    .ant-radio-button-wrapper {
      background-color: #2d2d30;
      border-color: #3c3c3c;
      color: #ffffff;
    }

    .ant-radio-button-wrapper:hover {
      background-color: #3c3c3c;
      border-color: #1890ff;
      color: #1890ff;
    }

    .ant-radio-button-wrapper-checked {
      background-color: #1890ff;
      border-color: #1890ff;
      color: #ffffff;
    }
  }

  &.dl-radio--card {
    .ant-radio-wrapper {
      padding: 12px;
      border: 1px solid #3c3c3c;
      border-radius: 6px;
      background-color: #2d2d30;
      transition: all 0.2s ease;
    }

    .ant-radio-wrapper:hover {
      border-color: #1890ff;
      background-color: #3c3c3c;
    }

    .ant-radio-wrapper-checked {
      border-color: #1890ff;
      background-color: rgba(24, 144, 255, 0.1);
    }
  }

  &.dl-radio--no-label {
    .ant-radio-wrapper {
      display: inline-block;
    }
  }
}

.dl-radio-group {
  .ant-radio-group-item {
    margin-right: 16px;
    margin-bottom: 8px;
  }

  &.dl-radio-group--vertical {
    display: flex;
    flex-direction: column;
    
    .ant-radio-group-item {
      margin-right: 0;
      margin-bottom: 12px;
    }
  }

  &.dl-radio-group--button {
    .ant-radio-button-wrapper:first-child {
      border-left: 1px solid #3c3c3c;
    }
  }
}
`

// 注入样式
if (typeof document !== 'undefined') {
  const styleId = 'dl-radio-styles'
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style')
    style.id = styleId
    style.textContent = radioStyles
    document.head.appendChild(style)
  }
}

// 导出组件和组
DLRadio.Group = DLRadioGroup
DLRadio.Button = Radio.Button

export default DLRadio
