/**
 * 缩放操作工具组件
 */

import React, { useRef, useState } from 'react'
import { useThree, useFrame } from '@react-three/fiber'
import { Mesh, Vector3 } from 'three'

export interface ScaleGizmoProps {
  size?: number
  onScaleStart?: () => void
  onScale?: (scale: Vector3) => void
  onScaleEnd?: () => void
}

const ScaleGizmo: React.FC<ScaleGizmoProps> = ({
  size = 1,
  onScaleStart,
  onScale,
  onScaleEnd
}) => {
  const { camera, raycaster, pointer } = useThree()
  const [isDragging, setIsDragging] = useState(false)
  const [activeAxis, setActiveAxis] = useState<'x' | 'y' | 'z' | 'uniform' | null>(null)

  // 轴向引用
  const xAxisRef = useRef<Mesh>(null)
  const yAxisRef = useRef<Mesh>(null)
  const zAxisRef = useRef<Mesh>(null)
  const centerRef = useRef<Mesh>(null)

  /**
   * 处理鼠标按下
   */
  const handlePointerDown = (axis: 'x' | 'y' | 'z' | 'uniform') => (event: any) => {
    event.stopPropagation()
    setIsDragging(true)
    setActiveAxis(axis)
    onScaleStart?.()
  }

  /**
   * 处理鼠标抬起
   */
  const handlePointerUp = () => {
    if (isDragging) {
      setIsDragging(false)
      setActiveAxis(null)
      onScaleEnd?.()
    }
  }

  /**
   * 处理鼠标移动
   */
  useFrame(() => {
    if (!isDragging || !activeAxis) return

    // 这里应该实现实际的缩放逻辑
    // 暂时使用模拟数据
    const scale = new Vector3(1.1, 1.1, 1.1)
    onScale?.(scale)
  })

  return (
    <group onPointerUp={handlePointerUp}>
      {/* X轴 - 红色 */}
      <group>
        <mesh position={[size, 0, 0]}>
          <cylinderGeometry args={[0.02 * size, 0.02 * size, size * 2, 8]} rotation={[0, 0, Math.PI / 2]} />
          <meshBasicMaterial color="red" />
        </mesh>
        <mesh
          ref={xAxisRef}
          position={[size * 2, 0, 0]}
          onPointerDown={handlePointerDown('x')}
        >
          <boxGeometry args={[0.2 * size, 0.2 * size, 0.2 * size]} />
          <meshBasicMaterial
            color={activeAxis === 'x' ? '#ff6666' : 'red'}
            transparent
            opacity={0.8}
          />
        </mesh>
      </group>
      
      {/* Y轴 - 绿色 */}
      <group>
        <mesh position={[0, size, 0]}>
          <cylinderGeometry args={[0.02 * size, 0.02 * size, size * 2, 8]} />
          <meshBasicMaterial color="green" />
        </mesh>
        <mesh
          ref={yAxisRef}
          position={[0, size * 2, 0]}
          onPointerDown={handlePointerDown('y')}
        >
          <boxGeometry args={[0.2 * size, 0.2 * size, 0.2 * size]} />
          <meshBasicMaterial
            color={activeAxis === 'y' ? '#66ff66' : 'green'}
            transparent
            opacity={0.8}
          />
        </mesh>
      </group>
      
      {/* Z轴 - 蓝色 */}
      <group>
        <mesh position={[0, 0, size]}>
          <cylinderGeometry args={[0.02 * size, 0.02 * size, size * 2, 8]} rotation={[Math.PI / 2, 0, 0]} />
          <meshBasicMaterial color="blue" />
        </mesh>
        <mesh
          ref={zAxisRef}
          position={[0, 0, size * 2]}
          onPointerDown={handlePointerDown('z')}
        >
          <boxGeometry args={[0.2 * size, 0.2 * size, 0.2 * size]} />
          <meshBasicMaterial
            color={activeAxis === 'z' ? '#6666ff' : 'blue'}
            transparent
            opacity={0.8}
          />
        </mesh>
      </group>
      
      {/* 中心统一缩放手柄 - 白色 */}
      <mesh
        ref={centerRef}
        position={[0, 0, 0]}
        onPointerDown={handlePointerDown('uniform')}
      >
        <boxGeometry args={[0.15 * size, 0.15 * size, 0.15 * size]} />
        <meshBasicMaterial
          color={activeAxis === 'uniform' ? '#cccccc' : 'white'}
          transparent
          opacity={0.8}
        />
      </mesh>
    </group>
  )
}

export default ScaleGizmo
