/**
 * 节点小地图组件
 * 
 * 显示整个脚本图的缩略图和当前视口位置
 */

import React, { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { ScriptGraph, NodeEditorState, Vector2 } from '../types'

/**
 * 节点小地图属性
 */
export interface NodeMinimapProps {
  /** 脚本图形 */
  graph: ScriptGraph
  /** 编辑器状态 */
  editorState: NodeEditorState
  /** 选中的节点 */
  selectedNodes: string[]
  /** 视口变化回调 */
  onViewportChange?: (zoom: number, pan: Vector2) => void
  /** 自定义类名 */
  className?: string
}

/**
 * 节点小地图组件
 */
const NodeMinimap: React.FC<NodeMinimapProps> = ({
  graph,
  editorState,
  selectedNodes,
  onViewportChange,
  className = ''
}) => {
  const { t } = useTranslation()
  
  // 小地图尺寸
  const minimapWidth = 200
  const minimapHeight = 150
  
  /**
   * 计算图形边界
   */
  const graphBounds = useMemo(() => {
    if (graph.nodes.length === 0) {
      return { minX: 0, minY: 0, maxX: 1000, maxY: 1000 }
    }
    
    let minX = Infinity
    let minY = Infinity
    let maxX = -Infinity
    let maxY = -Infinity
    
    graph.nodes.forEach(node => {
      minX = Math.min(minX, node.position.x)
      minY = Math.min(minY, node.position.y)
      maxX = Math.max(maxX, node.position.x + 150) // 假设节点宽度150
      maxY = Math.max(maxY, node.position.y + 100) // 假设节点高度100
    })
    
    // 添加一些边距
    const padding = 100
    return {
      minX: minX - padding,
      minY: minY - padding,
      maxX: maxX + padding,
      maxY: maxY + padding
    }
  }, [graph.nodes])
  
  /**
   * 计算缩放比例
   */
  const minimapScale = useMemo(() => {
    const graphWidth = graphBounds.maxX - graphBounds.minX
    const graphHeight = graphBounds.maxY - graphBounds.minY
    
    const scaleX = minimapWidth / graphWidth
    const scaleY = minimapHeight / graphHeight
    
    return Math.min(scaleX, scaleY, 1) // 最大不超过1:1
  }, [graphBounds, minimapWidth, minimapHeight])
  
  /**
   * 转换坐标到小地图坐标系
   */
  const worldToMinimap = useCallback((worldPos: Vector2): Vector2 => {
    return {
      x: (worldPos.x - graphBounds.minX) * minimapScale,
      y: (worldPos.y - graphBounds.minY) * minimapScale
    }
  }, [graphBounds, minimapScale])
  
  /**
   * 转换小地图坐标到世界坐标
   */
  const minimapToWorld = useCallback((minimapPos: Vector2): Vector2 => {
    return {
      x: minimapPos.x / minimapScale + graphBounds.minX,
      y: minimapPos.y / minimapScale + graphBounds.minY
    }
  }, [graphBounds, minimapScale])
  
  /**
   * 计算当前视口在小地图中的位置
   */
  const viewportRect = useMemo(() => {
    const viewportWidth = 1000 / editorState.zoom // 假设视口宽度
    const viewportHeight = 600 / editorState.zoom // 假设视口高度
    
    const topLeft = worldToMinimap({
      x: -editorState.pan.x / editorState.zoom,
      y: -editorState.pan.y / editorState.zoom
    })
    
    const bottomRight = worldToMinimap({
      x: (-editorState.pan.x + viewportWidth) / editorState.zoom,
      y: (-editorState.pan.y + viewportHeight) / editorState.zoom
    })
    
    return {
      x: topLeft.x,
      y: topLeft.y,
      width: bottomRight.x - topLeft.x,
      height: bottomRight.y - topLeft.y
    }
  }, [editorState, worldToMinimap])
  
  /**
   * 处理小地图点击
   */
  const handleMinimapClick = useCallback((event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect()
    const minimapPos = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    }
    
    const worldPos = minimapToWorld(minimapPos)
    
    // 计算新的平移值，使点击位置居中
    const newPan = {
      x: -worldPos.x * editorState.zoom + 500, // 视口中心
      y: -worldPos.y * editorState.zoom + 300
    }
    
    onViewportChange?.(editorState.zoom, newPan)
  }, [minimapToWorld, editorState.zoom, onViewportChange])
  
  /**
   * 渲染节点
   */
  const renderNodes = () => {
    return graph.nodes.map(node => {
      const pos = worldToMinimap(node.position)
      const isSelected = selectedNodes.includes(node.id)
      
      return (
        <rect
          key={node.id}
          x={pos.x}
          y={pos.y}
          width={150 * minimapScale}
          height={100 * minimapScale}
          fill={isSelected ? '#1890ff' : node.definition.color || '#ffffff'}
          stroke={isSelected ? '#1890ff' : '#d9d9d9'}
          strokeWidth="1"
          rx="2"
        />
      )
    })
  }
  
  /**
   * 渲染连接
   */
  const renderConnections = () => {
    return graph.connections.map(connection => {
      const sourceNode = graph.nodes.find(n => n.id === connection.sourceNodeId)
      const targetNode = graph.nodes.find(n => n.id === connection.targetNodeId)
      
      if (!sourceNode || !targetNode) return null
      
      const sourcePos = worldToMinimap({
        x: sourceNode.position.x + 150,
        y: sourceNode.position.y + 50
      })
      
      const targetPos = worldToMinimap({
        x: targetNode.position.x,
        y: targetNode.position.y + 50
      })
      
      return (
        <line
          key={connection.id}
          x1={sourcePos.x}
          y1={sourcePos.y}
          x2={targetPos.x}
          y2={targetPos.y}
          stroke="#999999"
          strokeWidth="1"
        />
      )
    })
  }
  
  return (
    <div className={`node-minimap ${className}`}>
      <div className="minimap-header">
        <span className="text-xs text-gray-600">{t('editor.minimap')}</span>
      </div>
      
      <svg
        width={minimapWidth}
        height={minimapHeight}
        className="minimap-svg"
        style={{
          border: '1px solid #d9d9d9',
          borderRadius: '4px',
          backgroundColor: '#fafafa',
          cursor: 'pointer'
        }}
        onClick={handleMinimapClick}
      >
        {/* 连接 */}
        {renderConnections()}
        
        {/* 节点 */}
        {renderNodes()}
        
        {/* 当前视口 */}
        <rect
          x={viewportRect.x}
          y={viewportRect.y}
          width={viewportRect.width}
          height={viewportRect.height}
          fill="rgba(24, 144, 255, 0.2)"
          stroke="#1890ff"
          strokeWidth="2"
          strokeDasharray="3,3"
        />
      </svg>
    </div>
  )
}

export default NodeMinimap
