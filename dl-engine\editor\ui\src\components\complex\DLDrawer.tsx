/**
 * DL-Engine 抽屉组件
 * 
 * 基于 Ant Design Drawer 的增强版本，专为编辑器场景优化
 */

import React, { useCallback, useEffect } from 'react'
import { Drawer, DrawerProps, Button, Space, Divider } from 'antd'
import { useTranslation } from 'react-i18next'
import { CloseOutlined, FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons'
import classNames from 'classnames'
import { useHookstate } from '@hookstate/core'
import DLButton from '../basic/DLButton'
import DLIcon from '../basic/DLIcon'

/**
 * 抽屉操作按钮配置
 */
export interface DrawerAction {
  key: string
  label: string
  icon?: string | React.ReactNode
  type?: 'primary' | 'default' | 'dashed' | 'link' | 'text'
  danger?: boolean
  disabled?: boolean
  loading?: boolean
  onClick: () => void
}

/**
 * DL抽屉属性
 */
export interface DLDrawerProps extends Omit<DrawerProps, 'title' | 'extra'> {
  /** 抽屉标题 */
  title?: string | React.ReactNode
  /** 抽屉副标题 */
  subtitle?: string
  /** 是否显示全屏按钮 */
  showFullscreen?: boolean
  /** 是否全屏显示 */
  fullscreen?: boolean
  /** 全屏状态变化回调 */
  onFullscreenChange?: (fullscreen: boolean) => void
  /** 底部操作按钮 */
  actions?: DrawerAction[]
  /** 是否显示底部操作栏 */
  showFooter?: boolean
  /** 自定义底部内容 */
  footer?: React.ReactNode
  /** 是否可调整大小 */
  resizable?: boolean
  /** 最小宽度/高度 */
  minSize?: number
  /** 最大宽度/高度 */
  maxSize?: number
  /** 大小变化回调 */
  onSizeChange?: (size: number) => void
  /** 是否显示加载状态 */
  loading?: boolean
  /** 错误状态 */
  error?: string | React.ReactNode
  /** 空状态 */
  empty?: string | React.ReactNode
  /** 自定义类名 */
  className?: string
}

/**
 * DL抽屉组件
 */
const DLDrawer: React.FC<DLDrawerProps> = ({
  title,
  subtitle,
  showFullscreen = true,
  fullscreen = false,
  onFullscreenChange,
  actions = [],
  showFooter = true,
  footer,
  resizable = false,
  minSize = 300,
  maxSize = 800,
  onSizeChange,
  loading = false,
  error,
  empty,
  className,
  children,
  onClose,
  ...props
}) => {
  const { t } = useTranslation()
  const isFullscreen = useHookstate(fullscreen)
  const currentSize = useHookstate(props.width || 520)

  /**
   * 切换全屏状态
   */
  const toggleFullscreen = useCallback(() => {
    const newFullscreen = !isFullscreen.get()
    isFullscreen.set(newFullscreen)
    onFullscreenChange?.(newFullscreen)
  }, [isFullscreen, onFullscreenChange])

  /**
   * 处理关闭
   */
  const handleClose = useCallback((e: any) => {
    onClose?.(e)
  }, [onClose])

  /**
   * 渲染标题栏
   */
  const renderTitle = () => {
    if (!title && !subtitle) return null

    return (
      <div className="dl-drawer-title">
        <div className="dl-drawer-title-content">
          {typeof title === 'string' ? (
            <h3 className="dl-drawer-title-text">{title}</h3>
          ) : (
            title
          )}
          {subtitle && (
            <p className="dl-drawer-subtitle">{subtitle}</p>
          )}
        </div>
        <div className="dl-drawer-title-actions">
          {showFullscreen && (
            <DLButton
              icon={isFullscreen.get() ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
              type="text"
              size="small"
              onClick={toggleFullscreen}
              tooltip={isFullscreen.get() ? t('common.exitFullscreen') : t('common.fullscreen')}
            />
          )}
          <DLButton
            icon={<CloseOutlined />}
            type="text"
            size="small"
            onClick={handleClose}
            tooltip={t('common.close')}
          />
        </div>
      </div>
    )
  }

  /**
   * 渲染底部操作栏
   */
  const renderFooter = () => {
    if (!showFooter) return null

    if (footer) {
      return <div className="dl-drawer-footer">{footer}</div>
    }

    if (actions.length === 0) return null

    return (
      <div className="dl-drawer-footer">
        <Space>
          {actions.map(action => (
            <DLButton
              key={action.key}
              type={action.type}
              danger={action.danger}
              disabled={action.disabled}
              loading={action.loading}
              icon={action.icon}
              onClick={action.onClick}
            >
              {action.label}
            </DLButton>
          ))}
        </Space>
      </div>
    )
  }

  /**
   * 渲染内容
   */
  const renderContent = () => {
    if (loading) {
      return (
        <div className="dl-drawer-loading">
          <DLIcon name="loading" spin />
          <span>{t('common.loading')}</span>
        </div>
      )
    }

    if (error) {
      return (
        <div className="dl-drawer-error">
          <DLIcon name="exclamation-circle" />
          <div>{error}</div>
        </div>
      )
    }

    if (empty && !children) {
      return (
        <div className="dl-drawer-empty">
          <DLIcon name="inbox" />
          <div>{empty}</div>
        </div>
      )
    }

    return children
  }

  /**
   * 计算抽屉属性
   */
  const drawerProps = {
    ...props,
    width: isFullscreen.get() ? '100vw' : currentSize.get(),
    height: isFullscreen.get() && props.placement === 'top' || props.placement === 'bottom' ? '100vh' : props.height,
    className: classNames(
      'dl-drawer',
      {
        'dl-drawer--fullscreen': isFullscreen.get(),
        'dl-drawer--resizable': resizable,
        'dl-drawer--loading': loading,
        'dl-drawer--error': !!error,
        'dl-drawer--empty': !!empty && !children
      },
      className
    ),
    title: renderTitle(),
    footer: renderFooter(),
    onClose: handleClose
  }

  return (
    <Drawer {...drawerProps}>
      <div className="dl-drawer-content">
        {renderContent()}
      </div>
    </Drawer>
  )
}

// 添加样式
const drawerStyles = `
.dl-drawer {
  .dl-drawer-title {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
  }

  .dl-drawer-title-content {
    flex: 1;
  }

  .dl-drawer-title-text {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
  }

  .dl-drawer-subtitle {
    margin: 4px 0 0 0;
    font-size: 12px;
    color: #888888;
  }

  .dl-drawer-title-actions {
    display: flex;
    gap: 4px;
    margin-left: 16px;
  }

  .dl-drawer-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .dl-drawer-footer {
    padding: 16px 0 0 0;
    border-top: 1px solid #3c3c3c;
    display: flex;
    justify-content: flex-end;
  }

  .dl-drawer-loading,
  .dl-drawer-error,
  .dl-drawer-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #888888;
    gap: 12px;
  }

  .dl-drawer-error {
    color: #ff4d4f;
  }

  &.dl-drawer--fullscreen {
    .ant-drawer-content-wrapper {
      width: 100vw !important;
      height: 100vh !important;
    }
  }
}
`

// 注入样式
if (typeof document !== 'undefined') {
  const styleId = 'dl-drawer-styles'
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style')
    style.id = styleId
    style.textContent = drawerStyles
    document.head.appendChild(style)
  }
}

export default DLDrawer
