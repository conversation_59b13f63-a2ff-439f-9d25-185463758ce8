/**
 * DL-Engine 插件系统
 * 
 * 提供完整的插件开发、管理和市场功能
 */

// 核心API
export { 
  Plugin,
  PluginType,
  PluginStatus,
  PluginPermission
} from './PluginAPI'

export type {
  PluginMetadata,
  PluginConfig,
  PluginContext,
  PluginLogger,
  IPluginAPI,
  EditorAPI,
  UIAPI,
  FileSystemAPI,
  HttpAPI,
  StorageAPI,
  CommandAPI,
  MenuAPI,
  KeybindingAPI,
  ThemeAPI,
  I18nAPI,
  PanelOptions,
  Panel,
  DialogOptions,
  ToolbarButtonOptions,
  RequestOptions,
  CommandHandler,
  Command,
  MenuItem,
  Keybinding,
  Theme
} from './PluginAPI'

// 管理器
export { default as PluginManager } from './PluginManager'
export type { PluginManagerConfig } from './PluginManager'

export { default as PluginLoader } from './PluginLoader'
export type { PluginLoaderConfig } from './PluginLoader'

export { default as PluginRegistry } from './PluginRegistry'
export type { RegistryEntry, PluginFilter } from './PluginRegistry'

// UI组件
export { default as PluginMarket } from './components/PluginMarket'
export type { PluginMarketProps } from './components/PluginMarket'

// 工具函数
export * from './utils'

/**
 * 插件系统配置
 */
export interface PluginSystemConfig {
  /** 插件目录 */
  pluginDir: string
  /** 是否启用开发模式 */
  devMode: boolean
  /** 是否启用热重载 */
  hotReload: boolean
  /** 插件超时时间（毫秒） */
  timeout: number
  /** 最大并发加载数 */
  maxConcurrentLoads: number
  /** 市场API端点 */
  marketApiEndpoint?: string
  /** 是否启用插件签名验证 */
  enableSignatureVerification: boolean
  /** 允许的权限列表 */
  allowedPermissions: PluginPermission[]
}

/**
 * 默认插件系统配置
 */
export const defaultPluginSystemConfig: PluginSystemConfig = {
  pluginDir: './plugins',
  devMode: false,
  hotReload: false,
  timeout: 30000,
  maxConcurrentLoads: 3,
  marketApiEndpoint: '/api/plugins',
  enableSignatureVerification: false,
  allowedPermissions: Object.values(PluginPermission)
}

/**
 * 插件系统类
 */
export class PluginSystem {
  private config: PluginSystemConfig
  private manager: PluginManager
  private api: IPluginAPI
  private isInitialized = false
  
  constructor(config: Partial<PluginSystemConfig> = {}, api: IPluginAPI) {
    this.config = { ...defaultPluginSystemConfig, ...config }
    this.api = api
    this.manager = new PluginManager({
      pluginDir: this.config.pluginDir,
      devMode: this.config.devMode,
      hotReload: this.config.hotReload,
      timeout: this.config.timeout,
      maxConcurrentLoads: this.config.maxConcurrentLoads
    }, api)
  }
  
  /**
   * 初始化插件系统
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      throw new Error('插件系统已经初始化')
    }
    
    try {
      await this.manager.initialize()
      this.isInitialized = true
      
      console.log('插件系统初始化完成')
    } catch (error) {
      throw new Error(`插件系统初始化失败: ${error.message}`)
    }
  }
  
  /**
   * 获取插件管理器
   */
  getManager(): PluginManager {
    return this.manager
  }
  
  /**
   * 获取插件API
   */
  getAPI(): IPluginAPI {
    return this.api
  }
  
  /**
   * 获取配置
   */
  getConfig(): PluginSystemConfig {
    return { ...this.config }
  }
  
  /**
   * 加载插件
   */
  async loadPlugin(pluginId: string): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('插件系统未初始化')
    }
    
    await this.manager.loadPlugin(pluginId)
  }
  
  /**
   * 卸载插件
   */
  async unloadPlugin(pluginId: string): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('插件系统未初始化')
    }
    
    await this.manager.unloadPlugin(pluginId)
  }
  
  /**
   * 激活插件
   */
  async activatePlugin(pluginId: string): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('插件系统未初始化')
    }
    
    await this.manager.activatePlugin(pluginId)
  }
  
  /**
   * 停用插件
   */
  async deactivatePlugin(pluginId: string): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('插件系统未初始化')
    }
    
    await this.manager.deactivatePlugin(pluginId)
  }
  
  /**
   * 获取所有插件
   */
  getAllPlugins() {
    return this.manager.getAllPlugins()
  }
  
  /**
   * 获取激活的插件
   */
  getActivePlugins() {
    return this.manager.getActivePlugins()
  }
  
  /**
   * 获取插件信息
   */
  getPlugin(pluginId: string) {
    return this.manager.getPlugin(pluginId)
  }
  
  /**
   * 更新插件配置
   */
  async updatePluginConfig(pluginId: string, config: Partial<PluginConfig>): Promise<void> {
    await this.manager.updatePluginConfig(pluginId, config)
  }
  
  /**
   * 重新加载插件
   */
  async reloadPlugin(pluginId: string): Promise<void> {
    await this.manager.reloadPlugin(pluginId)
  }
  
  /**
   * 安装插件
   */
  async installPlugin(pluginUrl: string): Promise<void> {
    // TODO: 实现插件安装逻辑
    // 1. 下载插件包
    // 2. 验证插件签名（如果启用）
    // 3. 解压到插件目录
    // 4. 注册插件
    // 5. 加载插件
    
    throw new Error('插件安装功能暂未实现')
  }
  
  /**
   * 卸载插件
   */
  async uninstallPlugin(pluginId: string): Promise<void> {
    // TODO: 实现插件卸载逻辑
    // 1. 停用插件
    // 2. 卸载插件
    // 3. 删除插件文件
    // 4. 清理注册信息
    
    await this.unloadPlugin(pluginId)
    // 删除文件的逻辑需要根据环境实现
  }
  
  /**
   * 检查插件更新
   */
  async checkPluginUpdates(): Promise<Array<{
    pluginId: string
    currentVersion: string
    latestVersion: string
    updateUrl: string
  }>> {
    // TODO: 实现插件更新检查
    // 1. 获取已安装插件列表
    // 2. 查询市场API获取最新版本
    // 3. 比较版本号
    // 4. 返回可更新的插件列表
    
    return []
  }
  
  /**
   * 更新插件
   */
  async updatePlugin(pluginId: string): Promise<void> {
    // TODO: 实现插件更新逻辑
    // 1. 下载新版本
    // 2. 备份当前版本
    // 3. 停用当前版本
    // 4. 安装新版本
    // 5. 激活新版本
    
    throw new Error('插件更新功能暂未实现')
  }
  
  /**
   * 导出插件配置
   */
  async exportPluginConfig(): Promise<string> {
    const plugins = this.getAllPlugins()
    const config = {
      version: '1.0.0',
      timestamp: Date.now(),
      plugins: plugins.map(plugin => ({
        id: plugin.metadata.id,
        enabled: plugin.config.enabled,
        settings: plugin.config.settings
      }))
    }
    
    return JSON.stringify(config, null, 2)
  }
  
  /**
   * 导入插件配置
   */
  async importPluginConfig(configData: string): Promise<void> {
    try {
      const config = JSON.parse(configData)
      
      if (!config.plugins || !Array.isArray(config.plugins)) {
        throw new Error('无效的配置格式')
      }
      
      for (const pluginConfig of config.plugins) {
        const plugin = this.getPlugin(pluginConfig.id)
        if (plugin) {
          await this.updatePluginConfig(pluginConfig.id, {
            enabled: pluginConfig.enabled,
            settings: pluginConfig.settings
          })
        }
      }
      
    } catch (error) {
      throw new Error(`导入插件配置失败: ${error.message}`)
    }
  }
  
  /**
   * 获取插件统计信息
   */
  getStatistics() {
    const plugins = this.getAllPlugins()
    const activePlugins = this.getActivePlugins()
    
    const stats = {
      total: plugins.length,
      active: activePlugins.length,
      inactive: plugins.length - activePlugins.length,
      byType: {} as Record<PluginType, number>,
      byStatus: {} as Record<PluginStatus, number>
    }
    
    plugins.forEach(plugin => {
      // 按类型统计
      const type = plugin.metadata.type
      stats.byType[type] = (stats.byType[type] || 0) + 1
      
      // 按状态统计
      const status = plugin.status
      stats.byStatus[status] = (stats.byStatus[status] || 0) + 1
    })
    
    return stats
  }
  
  /**
   * 销毁插件系统
   */
  async dispose(): Promise<void> {
    if (this.manager) {
      await this.manager.dispose()
    }
    
    this.isInitialized = false
  }
}

// 导入类型
import { PluginPermission, PluginConfig } from './PluginAPI'

export default PluginSystem
