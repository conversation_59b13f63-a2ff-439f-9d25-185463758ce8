/**
 * 节点编辑器组件
 * 
 * 可视化脚本的核心编辑器，支持节点拖拽、连接等功能
 */

import React, { useRef, useCallback, useState, useEffect } from 'react'
import { useHookstate } from '@hookstate/core'
import { useTranslation } from 'react-i18next'
import NodeCanvas from './NodeCanvas'
import NodeMinimap from './NodeMinimap'
import NodeToolbar from './NodeToolbar'
import ConnectionManager from './ConnectionManager'
import SelectionManager from './SelectionManager'
import { 
  ScriptGraph, 
  NodeInstance, 
  Connection, 
  NodeEditorConfig,
  ExecutionState,
  Vector2,
  NodeEditorState
} from '../types'

/**
 * 节点编辑器属性
 */
export interface NodeEditorProps {
  /** 脚本图形 */
  graph: ScriptGraph
  /** 编辑器配置 */
  config?: NodeEditorConfig
  /** 选中的节点 */
  selectedNodes?: string[]
  /** 选中的连接 */
  selectedConnections?: string[]
  /** 执行状态 */
  executionState?: ExecutionState
  /** 是否只读 */
  readonly?: boolean
  /** 节点添加回调 */
  onNodeAdd?: (nodeType: string, position: Vector2) => void
  /** 节点删除回调 */
  onNodeDelete?: (nodeIds: string[]) => void
  /** 节点选择回调 */
  onNodeSelect?: (nodeIds: string[]) => void
  /** 连接创建回调 */
  onConnectionCreate?: (connection: Partial<Connection>) => void
  /** 连接删除回调 */
  onConnectionDelete?: (connectionIds: string[]) => void
  /** 自定义类名 */
  className?: string
}

/**
 * 节点编辑器组件
 */
const NodeEditor: React.FC<NodeEditorProps> = ({
  graph,
  config = {},
  selectedNodes = [],
  selectedConnections = [],
  executionState = ExecutionState.IDLE,
  readonly = false,
  onNodeAdd,
  onNodeDelete,
  onNodeSelect,
  onConnectionCreate,
  onConnectionDelete,
  className = ''
}) => {
  const { t } = useTranslation()
  const canvasRef = useRef<HTMLDivElement>(null)
  const [editorState, setEditorState] = useState<NodeEditorState>({
    zoom: 1,
    pan: { x: 0, y: 0 },
    isDragging: false,
    isPanning: false,
    isConnecting: false,
    connectionStart: null,
    hoveredNode: null,
    hoveredSocket: null
  })
  
  /**
   * 处理画布点击
   */
  const handleCanvasClick = useCallback((event: React.MouseEvent) => {
    if (readonly) return
    
    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return
    
    const position = {
      x: (event.clientX - rect.left - editorState.pan.x) / editorState.zoom,
      y: (event.clientY - rect.top - editorState.pan.y) / editorState.zoom
    }
    
    // 如果没有点击到节点，清除选择
    if (event.target === canvasRef.current) {
      onNodeSelect?.([])
    }
  }, [readonly, editorState, onNodeSelect])
  
  /**
   * 处理节点拖拽
   */
  const handleNodeDrag = useCallback((nodeId: string, delta: Vector2) => {
    if (readonly) return
    
    // TODO: 实现节点拖拽逻辑
    console.log('拖拽节点:', nodeId, delta)
  }, [readonly])
  
  /**
   * 处理连接开始
   */
  const handleConnectionStart = useCallback((nodeId: string, socketId: string, socketType: 'input' | 'output') => {
    if (readonly) return
    
    setEditorState(prev => ({
      ...prev,
      isConnecting: true,
      connectionStart: { nodeId, socketId, socketType }
    }))
  }, [readonly])
  
  /**
   * 处理连接结束
   */
  const handleConnectionEnd = useCallback((nodeId: string, socketId: string, socketType: 'input' | 'output') => {
    if (readonly || !editorState.connectionStart) return
    
    const start = editorState.connectionStart
    
    // 检查连接是否有效
    if (start.nodeId !== nodeId && start.socketType !== socketType) {
      const connection: Partial<Connection> = {
        id: `${start.nodeId}_${start.socketId}_${nodeId}_${socketId}`,
        sourceNodeId: start.socketType === 'output' ? start.nodeId : nodeId,
        sourceSocketId: start.socketType === 'output' ? start.socketId : socketId,
        targetNodeId: start.socketType === 'output' ? nodeId : start.nodeId,
        targetSocketId: start.socketType === 'output' ? socketId : start.socketId
      }
      
      onConnectionCreate?.(connection)
    }
    
    setEditorState(prev => ({
      ...prev,
      isConnecting: false,
      connectionStart: null
    }))
  }, [readonly, editorState.connectionStart, onConnectionCreate])
  
  /**
   * 处理缩放
   */
  const handleZoom = useCallback((delta: number, center?: Vector2) => {
    setEditorState(prev => {
      const newZoom = Math.max(0.1, Math.min(3, prev.zoom + delta))
      return {
        ...prev,
        zoom: newZoom
      }
    })
  }, [])
  
  /**
   * 处理平移
   */
  const handlePan = useCallback((delta: Vector2) => {
    setEditorState(prev => ({
      ...prev,
      pan: {
        x: prev.pan.x + delta.x,
        y: prev.pan.y + delta.y
      }
    }))
  }, [])
  
  /**
   * 处理键盘事件
   */
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (readonly) return
    
    switch (event.key) {
      case 'Delete':
      case 'Backspace':
        if (selectedNodes.length > 0) {
          onNodeDelete?.(selectedNodes)
        }
        if (selectedConnections.length > 0) {
          onConnectionDelete?.(selectedConnections)
        }
        break
      
      case 'Escape':
        onNodeSelect?.([])
        setEditorState(prev => ({
          ...prev,
          isConnecting: false,
          connectionStart: null
        }))
        break
      
      case 'a':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault()
          onNodeSelect?.(graph.nodes.map(node => node.id))
        }
        break
    }
  }, [readonly, selectedNodes, selectedConnections, graph.nodes, onNodeDelete, onConnectionDelete, onNodeSelect])
  
  /**
   * 绑定键盘事件
   */
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleKeyDown])
  
  return (
    <div className={`node-editor ${className}`}>
      {/* 工具栏 */}
      <NodeToolbar
        editorState={editorState}
        onZoomIn={() => handleZoom(0.1)}
        onZoomOut={() => handleZoom(-0.1)}
        onZoomReset={() => setEditorState(prev => ({ ...prev, zoom: 1, pan: { x: 0, y: 0 } }))}
        onFitToScreen={() => {
          // TODO: 实现适应屏幕逻辑
        }}
      />
      
      {/* 主画布区域 */}
      <div className="node-editor-main flex-1 relative overflow-hidden">
        {/* 画布 */}
        <NodeCanvas
          ref={canvasRef}
          graph={graph}
          editorState={editorState}
          selectedNodes={selectedNodes}
          selectedConnections={selectedConnections}
          executionState={executionState}
          readonly={readonly}
          onClick={handleCanvasClick}
          onNodeDrag={handleNodeDrag}
          onConnectionStart={handleConnectionStart}
          onConnectionEnd={handleConnectionEnd}
          onZoom={handleZoom}
          onPan={handlePan}
        />
        
        {/* 连接管理器 */}
        <ConnectionManager
          graph={graph}
          editorState={editorState}
          selectedConnections={selectedConnections}
          onConnectionSelect={(connectionIds) => {
            // TODO: 实现连接选择逻辑
          }}
        />
        
        {/* 选择管理器 */}
        <SelectionManager
          selectedNodes={selectedNodes}
          selectedConnections={selectedConnections}
          onSelectionChange={(nodes, connections) => {
            onNodeSelect?.(nodes)
            // TODO: 实现连接选择回调
          }}
        />
        
        {/* 小地图 */}
        <NodeMinimap
          graph={graph}
          editorState={editorState}
          selectedNodes={selectedNodes}
          onViewportChange={(zoom, pan) => {
            setEditorState(prev => ({ ...prev, zoom, pan }))
          }}
          className="absolute bottom-4 right-4"
        />
      </div>
    </div>
  )
}

export default NodeEditor
