/**
 * DL-Engine 输入框组件
 * 
 * 基于 Ant Design Input 的增强版本
 */

import React, { useCallback } from 'react'
import { Input, InputProps } from 'antd'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'
import DLIcon from './DLIcon'

/**
 * DL输入框属性
 */
export interface DLInputProps extends InputProps {
  /** 输入框标签 */
  label?: string
  /** 是否显示字符计数 */
  showCount?: boolean
  /** 是否显示清除按钮 */
  allowClear?: boolean
  /** 输入验证状态 */
  status?: 'error' | 'warning' | 'success'
  /** 验证消息 */
  message?: string
  /** 自定义类名 */
  className?: string
}

/**
 * DL输入框组件
 */
const DLInput: React.FC<DLInputProps> = ({
  label,
  showCount = false,
  allowClear = true,
  status,
  message,
  className,
  ...props
}) => {
  const { t } = useTranslation()
  
  /**
   * 获取状态图标
   */
  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <DLIcon name="check-circle" className="text-green-500" />
      case 'warning':
        return <DLIcon name="exclamation-circle" className="text-yellow-500" />
      case 'error':
        return <DLIcon name="close-circle" className="text-red-500" />
      default:
        return null
    }
  }
  
  /**
   * 输入框类名
   */
  const inputClassName = classNames(
    'dl-input',
    {
      'dl-input--with-label': label,
      'dl-input--error': status === 'error',
      'dl-input--warning': status === 'warning',
      'dl-input--success': status === 'success'
    },
    className
  )
  
  return (
    <div className={inputClassName}>
      {/* 标签 */}
      {label && (
        <div className="dl-input-label mb-1">
          <span className="text-sm text-gray-700">{label}</span>
        </div>
      )}
      
      {/* 输入框 */}
      <Input
        {...props}
        showCount={showCount}
        allowClear={allowClear}
        status={status}
        suffix={getStatusIcon()}
      />
      
      {/* 验证消息 */}
      {message && (
        <div className={`dl-input-message mt-1 text-xs ${
          status === 'error' ? 'text-red-500' :
          status === 'warning' ? 'text-yellow-500' :
          status === 'success' ? 'text-green-500' :
          'text-gray-500'
        }`}>
          {message}
        </div>
      )}
    </div>
  )
}

export default DLInput
