/**
 * 连接渲染器组件
 * 
 * 渲染节点之间的连接线
 */

import React, { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { Connection, ScriptGraph, Vector2, DataType } from '../types'

/**
 * 连接渲染器属性
 */
export interface ConnectionRendererProps {
  /** 连接定义 */
  connection: Connection & { temporary?: boolean }
  /** 脚本图形 */
  graph: ScriptGraph
  /** 是否选中 */
  selected: boolean
  /** 是否正在执行 */
  executing: boolean
  /** 是否只读 */
  readonly: boolean
  /** 点击回调 */
  onClick?: () => void
  /** 自定义类名 */
  className?: string
}

/**
 * 获取数据类型颜色
 */
const getDataTypeColor = (dataType: DataType): string => {
  switch (dataType) {
    case DataType.EXECUTION:
      return '#ffffff'
    case DataType.BOOLEAN:
      return '#ff6b6b'
    case DataType.NUMBER:
      return '#4ecdc4'
    case DataType.STRING:
      return '#ffe66d'
    case DataType.VECTOR2:
      return '#a8e6cf'
    case DataType.VECTOR3:
      return '#88d8c0'
    case DataType.COLOR:
      return '#ffd93d'
    case DataType.OBJECT:
      return '#6c5ce7'
    case DataType.ANY:
      return '#95a5a6'
    default:
      return '#bdc3c7'
  }
}

/**
 * 计算贝塞尔曲线路径
 */
const calculateBezierPath = (start: Vector2, end: Vector2, curvature: number = 0.3): string => {
  const dx = end.x - start.x
  const dy = end.y - start.y
  
  // 控制点偏移
  const cp1x = start.x + dx * curvature
  const cp1y = start.y
  const cp2x = end.x - dx * curvature
  const cp2y = end.y
  
  return `M ${start.x} ${start.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${end.x} ${end.y}`
}

/**
 * 连接渲染器组件
 */
const ConnectionRenderer: React.FC<ConnectionRendererProps> = ({
  connection,
  graph,
  selected,
  executing,
  readonly,
  onClick,
  className = ''
}) => {
  const { t } = useTranslation()
  
  /**
   * 计算连接路径和样式
   */
  const connectionData = useMemo(() => {
    // 查找源节点和目标节点
    const sourceNode = graph.nodes.find(node => node.id === connection.sourceNodeId)
    const targetNode = graph.nodes.find(node => node.id === connection.targetNodeId)
    
    if (!sourceNode || (!targetNode && !connection.temporary)) {
      return null
    }
    
    // 查找源插槽和目标插槽
    const sourceSocket = sourceNode.definition.outputs?.find(
      socket => socket.id === connection.sourceSocketId
    )
    
    let targetSocket = null
    if (targetNode) {
      targetSocket = targetNode.definition.inputs?.find(
        socket => socket.id === connection.targetSocketId
      )
    }
    
    if (!sourceSocket) {
      return null
    }
    
    // 计算插槽位置
    const sourcePos = {
      x: sourceNode.position.x + 150, // 节点宽度
      y: sourceNode.position.y + 30 + (sourceNode.definition.outputs?.indexOf(sourceSocket) || 0) * 25
    }
    
    let targetPos: Vector2
    if (targetNode && targetSocket) {
      targetPos = {
        x: targetNode.position.x,
        y: targetNode.position.y + 30 + (targetNode.definition.inputs?.indexOf(targetSocket) || 0) * 25
      }
    } else {
      // 临时连接，跟随鼠标
      targetPos = { x: sourcePos.x + 100, y: sourcePos.y }
    }
    
    // 计算路径
    const path = calculateBezierPath(sourcePos, targetPos)
    
    // 获取连接颜色
    const color = getDataTypeColor(sourceSocket.dataType)
    
    return {
      path,
      color,
      sourcePos,
      targetPos,
      dataType: sourceSocket.dataType,
      isExecution: sourceSocket.isExecution
    }
  }, [connection, graph])
  
  if (!connectionData) {
    return null
  }
  
  /**
   * 获取连接样式
   */
  const getConnectionStyle = () => {
    const baseStyle = {
      stroke: connectionData.color,
      strokeWidth: connectionData.isExecution ? 3 : 2,
      fill: 'none',
      cursor: readonly ? 'default' : 'pointer'
    }
    
    if (selected) {
      return {
        ...baseStyle,
        strokeWidth: baseStyle.strokeWidth + 1,
        filter: 'drop-shadow(0 0 4px rgba(24, 144, 255, 0.6))'
      }
    }
    
    if (executing) {
      return {
        ...baseStyle,
        strokeDasharray: '5,5',
        animation: 'dash 1s linear infinite'
      }
    }
    
    if (connection.temporary) {
      return {
        ...baseStyle,
        strokeDasharray: '3,3',
        opacity: 0.7
      }
    }
    
    return baseStyle
  }
  
  /**
   * 处理点击
   */
  const handleClick = (event: React.MouseEvent) => {
    if (readonly) return
    
    event.stopPropagation()
    onClick?.()
  }
  
  return (
    <g className={`connection-renderer ${className}`}>
      {/* 连接线背景（用于更好的点击检测） */}
      <path
        d={connectionData.path}
        stroke="transparent"
        strokeWidth="10"
        fill="none"
        onClick={handleClick}
        style={{ cursor: readonly ? 'default' : 'pointer' }}
      />
      
      {/* 连接线 */}
      <path
        d={connectionData.path}
        style={getConnectionStyle()}
        onClick={handleClick}
      />
      
      {/* 执行流动画 */}
      {executing && connectionData.isExecution && (
        <circle
          r="3"
          fill={connectionData.color}
          opacity="0.8"
        >
          <animateMotion
            dur="1s"
            repeatCount="indefinite"
            path={connectionData.path}
          />
        </circle>
      )}
      
      {/* 数据流动画 */}
      {executing && !connectionData.isExecution && (
        <circle
          r="2"
          fill={connectionData.color}
          opacity="0.6"
        >
          <animateMotion
            dur="2s"
            repeatCount="indefinite"
            path={connectionData.path}
          />
        </circle>
      )}
    </g>
  )
}

export default ConnectionRenderer
