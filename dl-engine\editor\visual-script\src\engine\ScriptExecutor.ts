/**
 * 可视化脚本执行引擎
 * 
 * 负责执行可视化脚本图的核心引擎
 */

import { NodeDefinition, NodeInstance, Connection, ExecutionContext, DataType } from '../types'
import { EducationNodes } from '../education/nodes'

export interface ExecutionResult {
  success: boolean
  error?: string
  outputs?: Record<string, any>
  executionTime?: number
}

export interface ExecutionOptions {
  /** 是否启用调试模式 */
  debug?: boolean
  /** 执行超时时间（毫秒） */
  timeout?: number
  /** 是否启用性能分析 */
  profiling?: boolean
  /** 最大执行步数 */
  maxSteps?: number
}

/**
 * 脚本执行器类
 */
export class ScriptExecutor {
  private nodes: Map<string, NodeInstance> = new Map()
  private connections: Connection[] = []
  private nodeDefinitions: Map<string, NodeDefinition> = new Map()
  private executionStack: string[] = []
  private isExecuting = false
  private debugMode = false
  private profilingData: Map<string, number> = new Map()

  constructor() {
    this.registerBuiltinNodes()
  }

  /**
   * 注册内置节点
   */
  private registerBuiltinNodes(): void {
    EducationNodes.forEach(nodeDef => {
      this.nodeDefinitions.set(nodeDef.type, nodeDef)
    })
  }

  /**
   * 注册自定义节点定义
   */
  registerNodeDefinition(definition: NodeDefinition): void {
    this.nodeDefinitions.set(definition.type, definition)
  }

  /**
   * 添加节点实例
   */
  addNode(node: NodeInstance): void {
    this.nodes.set(node.id, node)
  }

  /**
   * 移除节点实例
   */
  removeNode(nodeId: string): void {
    this.nodes.delete(nodeId)
    // 移除相关连接
    this.connections = this.connections.filter(
      conn => conn.fromNodeId !== nodeId && conn.toNodeId !== nodeId
    )
  }

  /**
   * 添加连接
   */
  addConnection(connection: Connection): void {
    this.connections.push(connection)
  }

  /**
   * 移除连接
   */
  removeConnection(connectionId: string): void {
    this.connections = this.connections.filter(conn => conn.id !== connectionId)
  }

  /**
   * 清空所有节点和连接
   */
  clear(): void {
    this.nodes.clear()
    this.connections = []
    this.executionStack = []
    this.profilingData.clear()
  }

  /**
   * 执行脚本图
   */
  async execute(options: ExecutionOptions = {}): Promise<ExecutionResult> {
    if (this.isExecuting) {
      return {
        success: false,
        error: '脚本正在执行中'
      }
    }

    const startTime = performance.now()
    this.isExecuting = true
    this.debugMode = options.debug || false
    this.executionStack = []
    this.profilingData.clear()

    try {
      // 查找入口节点（没有执行输入的节点）
      const entryNodes = this.findEntryNodes()
      
      if (entryNodes.length === 0) {
        return {
          success: false,
          error: '未找到入口节点'
        }
      }

      // 创建执行上下文
      const context: ExecutionContext = {
        variables: new Map(),
        functions: new Map(),
        debug: this.debugMode,
        profiling: options.profiling || false
      }

      // 执行所有入口节点
      for (const entryNode of entryNodes) {
        await this.executeNode(entryNode.id, context, options)
      }

      const executionTime = performance.now() - startTime

      return {
        success: true,
        executionTime,
        outputs: this.collectOutputs()
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        executionTime: performance.now() - startTime
      }
    } finally {
      this.isExecuting = false
    }
  }

  /**
   * 查找入口节点
   */
  private findEntryNodes(): NodeInstance[] {
    const entryNodes: NodeInstance[] = []
    
    for (const node of this.nodes.values()) {
      const definition = this.nodeDefinitions.get(node.type)
      if (!definition) continue

      // 检查是否有执行输入
      const hasExecutionInput = definition.inputs.some(input => input.isExecution)
      
      if (!hasExecutionInput) {
        entryNodes.push(node)
        continue
      }

      // 检查是否有连接到执行输入的连接
      const hasExecutionConnection = this.connections.some(conn => 
        conn.toNodeId === node.id && 
        definition.inputs.find(input => input.id === conn.toSocketId)?.isExecution
      )

      if (!hasExecutionConnection) {
        entryNodes.push(node)
      }
    }

    return entryNodes
  }

  /**
   * 执行单个节点
   */
  private async executeNode(
    nodeId: string, 
    context: ExecutionContext, 
    options: ExecutionOptions
  ): Promise<any> {
    const node = this.nodes.get(nodeId)
    if (!node) {
      throw new Error(`节点 ${nodeId} 不存在`)
    }

    const definition = this.nodeDefinitions.get(node.type)
    if (!definition) {
      throw new Error(`节点类型 ${node.type} 未注册`)
    }

    // 检查执行栈深度
    if (this.executionStack.length > (options.maxSteps || 1000)) {
      throw new Error('执行步数超过限制，可能存在无限循环')
    }

    this.executionStack.push(nodeId)

    try {
      const nodeStartTime = performance.now()

      // 收集输入数据
      const inputs = await this.collectInputs(node, context, options)

      // 调试输出
      if (this.debugMode) {
        console.log(`执行节点: ${definition.name} (${nodeId})`, inputs)
      }

      // 执行节点
      const outputs = await definition.execute(inputs, context)

      // 记录性能数据
      if (options.profiling) {
        const executionTime = performance.now() - nodeStartTime
        this.profilingData.set(nodeId, executionTime)
      }

      // 传播输出到连接的节点
      await this.propagateOutputs(node, outputs, context, options)

      return outputs

    } finally {
      this.executionStack.pop()
    }
  }

  /**
   * 收集节点输入
   */
  private async collectInputs(
    node: NodeInstance, 
    context: ExecutionContext, 
    options: ExecutionOptions
  ): Promise<Record<string, any>> {
    const definition = this.nodeDefinitions.get(node.type)
    if (!definition) return {}

    const inputs: Record<string, any> = {}

    for (const inputDef of definition.inputs) {
      // 查找连接到此输入的连接
      const connection = this.connections.find(conn => 
        conn.toNodeId === node.id && conn.toSocketId === inputDef.id
      )

      if (connection) {
        // 从连接的节点获取输出值
        const sourceNode = this.nodes.get(connection.fromNodeId)
        if (sourceNode) {
          // 如果是执行连接，先执行源节点
          if (inputDef.isExecution) {
            await this.executeNode(connection.fromNodeId, context, options)
          } else {
            // 获取源节点的输出值
            const sourceOutputs = await this.getNodeOutputs(connection.fromNodeId, context)
            inputs[inputDef.id] = sourceOutputs[connection.fromSocketId]
          }
        }
      } else {
        // 使用节点实例的值或默认值
        inputs[inputDef.id] = node.inputs[inputDef.id] ?? inputDef.defaultValue
      }
    }

    return inputs
  }

  /**
   * 获取节点输出
   */
  private async getNodeOutputs(nodeId: string, context: ExecutionContext): Promise<Record<string, any>> {
    // 这里应该返回节点的缓存输出值
    // 暂时返回空对象
    return {}
  }

  /**
   * 传播输出到连接的节点
   */
  private async propagateOutputs(
    node: NodeInstance, 
    outputs: Record<string, any>, 
    context: ExecutionContext, 
    options: ExecutionOptions
  ): Promise<void> {
    const definition = this.nodeDefinitions.get(node.type)
    if (!definition) return

    // 查找从此节点输出的连接
    const outputConnections = this.connections.filter(conn => conn.fromNodeId === node.id)

    for (const connection of outputConnections) {
      const outputDef = definition.outputs.find(output => output.id === connection.fromSocketId)
      if (!outputDef) continue

      // 如果是执行输出，执行目标节点
      if (outputDef.isExecution) {
        await this.executeNode(connection.toNodeId, context, options)
      }
    }
  }

  /**
   * 收集所有输出
   */
  private collectOutputs(): Record<string, any> {
    // 这里应该收集所有节点的输出值
    // 暂时返回空对象
    return {}
  }

  /**
   * 停止执行
   */
  stop(): void {
    this.isExecuting = false
  }

  /**
   * 获取执行状态
   */
  isRunning(): boolean {
    return this.isExecuting
  }

  /**
   * 获取性能分析数据
   */
  getProfilingData(): Map<string, number> {
    return new Map(this.profilingData)
  }

  /**
   * 获取执行栈
   */
  getExecutionStack(): string[] {
    return [...this.executionStack]
  }
}

export default ScriptExecutor
