/**
 * XR交互系统
 * 
 * 处理VR/AR环境中的用户交互
 */

import * as THREE from 'three'
import { EventEmitter } from 'events'
import WebLayer3D from './WebLayer3D'

/**
 * 交互类型
 */
export enum XRInteractionType {
  POINT = 'point',
  GRAB = 'grab',
  PINCH = 'pinch',
  TOUCH = 'touch',
  GAZE = 'gaze'
}

/**
 * 交互事件
 */
export interface XRInteractionEvent {
  type: XRInteractionType
  controllerId: string
  target: THREE.Object3D | null
  position: THREE.Vector3
  rotation: THREE.Quaternion
  intensity: number
  timestamp: number
}

/**
 * 可交互对象接口
 */
export interface XRInteractable {
  onInteractionStart?(event: XRInteractionEvent): void
  onInteractionUpdate?(event: XRInteractionEvent): void
  onInteractionEnd?(event: XRInteractionEvent): void
  onHover?(event: XRInteractionEvent): void
  onUnhover?(event: XRInteractionEvent): void
}

/**
 * 控制器状态
 */
interface ControllerState {
  id: string
  object3D: THREE.Object3D
  raycaster: THREE.Raycaster
  isPointing: boolean
  isGrabbing: boolean
  currentTarget: THREE.Object3D | null
  hoveredTarget: THREE.Object3D | null
  grabOffset: THREE.Vector3
}

/**
 * XR交互系统类
 */
export class XRInteractionSystem extends EventEmitter {
  private scene: THREE.Scene
  private camera: THREE.Camera
  private controllers: Map<string, ControllerState> = new Map()
  private interactables: Set<THREE.Object3D & XRInteractable> = new Set()
  private webLayers: Set<WebLayer3D> = new Set()
  private raycastTargets: THREE.Object3D[] = []
  
  constructor(scene: THREE.Scene, camera: THREE.Camera) {
    super()
    this.scene = scene
    this.camera = camera
  }
  
  /**
   * 添加控制器
   */
  addController(id: string, object3D: THREE.Object3D): void {
    const controller: ControllerState = {
      id,
      object3D,
      raycaster: new THREE.Raycaster(),
      isPointing: false,
      isGrabbing: false,
      currentTarget: null,
      hoveredTarget: null,
      grabOffset: new THREE.Vector3()
    }
    
    this.controllers.set(id, controller)
    
    // 添加控制器可视化
    this.addControllerVisual(controller)
  }
  
  /**
   * 移除控制器
   */
  removeController(id: string): void {
    const controller = this.controllers.get(id)
    if (controller) {
      // 结束当前交互
      if (controller.currentTarget) {
        this.endInteraction(controller, controller.currentTarget)
      }
      
      this.controllers.delete(id)
    }
  }
  
  /**
   * 添加可交互对象
   */
  addInteractable(object: THREE.Object3D & XRInteractable): void {
    this.interactables.add(object)
    this.raycastTargets.push(object)
  }
  
  /**
   * 移除可交互对象
   */
  removeInteractable(object: THREE.Object3D & XRInteractable): void {
    this.interactables.delete(object)
    const index = this.raycastTargets.indexOf(object)
    if (index !== -1) {
      this.raycastTargets.splice(index, 1)
    }
  }
  
  /**
   * 添加Web层
   */
  addWebLayer(webLayer: WebLayer3D): void {
    this.webLayers.add(webLayer)
    this.raycastTargets.push(webLayer)
  }
  
  /**
   * 移除Web层
   */
  removeWebLayer(webLayer: WebLayer3D): void {
    this.webLayers.delete(webLayer)
    const index = this.raycastTargets.indexOf(webLayer)
    if (index !== -1) {
      this.raycastTargets.splice(index, 1)
    }
  }
  
  /**
   * 更新控制器状态
   */
  updateController(id: string, position: THREE.Vector3, rotation: THREE.Quaternion, buttons: boolean[]): void {
    const controller = this.controllers.get(id)
    if (!controller) return
    
    // 更新控制器位置和旋转
    controller.object3D.position.copy(position)
    controller.object3D.quaternion.copy(rotation)
    
    // 更新射线投射器
    const direction = new THREE.Vector3(0, 0, -1)
    direction.applyQuaternion(rotation)
    controller.raycaster.set(position, direction)
    
    // 处理按钮输入
    const triggerPressed = buttons[0] || false
    const gripPressed = buttons[1] || false
    
    this.handleControllerInput(controller, triggerPressed, gripPressed)
  }
  
  /**
   * 处理控制器输入
   */
  private handleControllerInput(controller: ControllerState, triggerPressed: boolean, gripPressed: boolean): void {
    // 射线投射检测
    const intersects = controller.raycaster.intersectObjects(this.raycastTargets, true)
    const target = intersects.length > 0 ? intersects[0].object : null
    
    // 处理悬停
    this.handleHover(controller, target)
    
    // 处理指向交互
    if (triggerPressed && !controller.isPointing) {
      this.startPointing(controller, target)
    } else if (!triggerPressed && controller.isPointing) {
      this.endPointing(controller)
    }
    
    // 处理抓取交互
    if (gripPressed && !controller.isGrabbing) {
      this.startGrabbing(controller, target)
    } else if (!gripPressed && controller.isGrabbing) {
      this.endGrabbing(controller)
    }
    
    // 更新抓取对象位置
    if (controller.isGrabbing && controller.currentTarget) {
      this.updateGrabbedObject(controller)
    }
  }
  
  /**
   * 处理悬停
   */
  private handleHover(controller: ControllerState, target: THREE.Object3D | null): void {
    if (controller.hoveredTarget !== target) {
      // 结束之前的悬停
      if (controller.hoveredTarget) {
        this.endHover(controller, controller.hoveredTarget)
      }
      
      // 开始新的悬停
      if (target) {
        this.startHover(controller, target)
      }
      
      controller.hoveredTarget = target
    }
  }
  
  /**
   * 开始悬停
   */
  private startHover(controller: ControllerState, target: THREE.Object3D): void {
    const event: XRInteractionEvent = {
      type: XRInteractionType.POINT,
      controllerId: controller.id,
      target,
      position: controller.object3D.position.clone(),
      rotation: controller.object3D.quaternion.clone(),
      intensity: 0,
      timestamp: Date.now()
    }
    
    // 调用对象的悬停方法
    if ('onHover' in target && typeof target.onHover === 'function') {
      target.onHover(event)
    }
    
    // 处理Web层悬停
    if (target instanceof WebLayer3D) {
      const pointer = new THREE.Vector2()
      const intersection = controller.raycaster.intersectObject(target)[0]
      if (intersection && intersection.uv) {
        pointer.x = intersection.uv.x * 2 - 1
        pointer.y = -(intersection.uv.y * 2 - 1)
        target.handleRaycast(this.camera, pointer)
      }
    }
    
    this.emit('hover', event)
  }
  
  /**
   * 结束悬停
   */
  private endHover(controller: ControllerState, target: THREE.Object3D): void {
    const event: XRInteractionEvent = {
      type: XRInteractionType.POINT,
      controllerId: controller.id,
      target,
      position: controller.object3D.position.clone(),
      rotation: controller.object3D.quaternion.clone(),
      intensity: 0,
      timestamp: Date.now()
    }
    
    // 调用对象的取消悬停方法
    if ('onUnhover' in target && typeof target.onUnhover === 'function') {
      target.onUnhover(event)
    }
    
    this.emit('unhover', event)
  }
  
  /**
   * 开始指向
   */
  private startPointing(controller: ControllerState, target: THREE.Object3D | null): void {
    controller.isPointing = true
    controller.currentTarget = target
    
    if (target) {
      this.startInteraction(controller, target, XRInteractionType.POINT)
    }
  }
  
  /**
   * 结束指向
   */
  private endPointing(controller: ControllerState): void {
    controller.isPointing = false
    
    if (controller.currentTarget) {
      this.endInteraction(controller, controller.currentTarget)
      controller.currentTarget = null
    }
  }
  
  /**
   * 开始抓取
   */
  private startGrabbing(controller: ControllerState, target: THREE.Object3D | null): void {
    controller.isGrabbing = true
    controller.currentTarget = target
    
    if (target) {
      // 计算抓取偏移
      controller.grabOffset.copy(target.position).sub(controller.object3D.position)
      this.startInteraction(controller, target, XRInteractionType.GRAB)
    }
  }
  
  /**
   * 结束抓取
   */
  private endGrabbing(controller: ControllerState): void {
    controller.isGrabbing = false
    
    if (controller.currentTarget) {
      this.endInteraction(controller, controller.currentTarget)
      controller.currentTarget = null
    }
  }
  
  /**
   * 更新被抓取的对象
   */
  private updateGrabbedObject(controller: ControllerState): void {
    if (controller.currentTarget) {
      const newPosition = controller.object3D.position.clone().add(controller.grabOffset)
      controller.currentTarget.position.copy(newPosition)
    }
  }
  
  /**
   * 开始交互
   */
  private startInteraction(controller: ControllerState, target: THREE.Object3D, type: XRInteractionType): void {
    const event: XRInteractionEvent = {
      type,
      controllerId: controller.id,
      target,
      position: controller.object3D.position.clone(),
      rotation: controller.object3D.quaternion.clone(),
      intensity: 1,
      timestamp: Date.now()
    }
    
    // 调用对象的交互开始方法
    if ('onInteractionStart' in target && typeof target.onInteractionStart === 'function') {
      target.onInteractionStart(event)
    }
    
    // 处理Web层点击
    if (target instanceof WebLayer3D && type === XRInteractionType.POINT) {
      const intersection = controller.raycaster.intersectObject(target)[0]
      if (intersection && intersection.uv) {
        const pixelX = intersection.uv.x * 1000 // 假设1000像素宽度
        const pixelY = (1 - intersection.uv.y) * 750 // 假设750像素高度
        target.simulateMouseEvent('click', pixelX, pixelY)
      }
    }
    
    this.emit('interactionstart', event)
  }
  
  /**
   * 结束交互
   */
  private endInteraction(controller: ControllerState, target: THREE.Object3D): void {
    const event: XRInteractionEvent = {
      type: controller.isGrabbing ? XRInteractionType.GRAB : XRInteractionType.POINT,
      controllerId: controller.id,
      target,
      position: controller.object3D.position.clone(),
      rotation: controller.object3D.quaternion.clone(),
      intensity: 0,
      timestamp: Date.now()
    }
    
    // 调用对象的交互结束方法
    if ('onInteractionEnd' in target && typeof target.onInteractionEnd === 'function') {
      target.onInteractionEnd(event)
    }
    
    this.emit('interactionend', event)
  }
  
  /**
   * 添加控制器可视化
   */
  private addControllerVisual(controller: ControllerState): void {
    // 创建控制器射线
    const rayGeometry = new THREE.BufferGeometry().setFromPoints([
      new THREE.Vector3(0, 0, 0),
      new THREE.Vector3(0, 0, -5)
    ])
    const rayMaterial = new THREE.LineBasicMaterial({ color: 0x00ff00 })
    const rayLine = new THREE.Line(rayGeometry, rayMaterial)
    
    controller.object3D.add(rayLine)
    
    // 创建控制器模型（简化版本）
    const controllerGeometry = new THREE.BoxGeometry(0.05, 0.05, 0.15)
    const controllerMaterial = new THREE.MeshBasicMaterial({ color: 0x333333 })
    const controllerMesh = new THREE.Mesh(controllerGeometry, controllerMaterial)
    
    controller.object3D.add(controllerMesh)
  }
  
  /**
   * 更新系统
   */
  update(): void {
    // 更新所有Web层
    this.webLayers.forEach(webLayer => {
      webLayer.update()
    })
  }
  
  /**
   * 销毁系统
   */
  dispose(): void {
    this.controllers.clear()
    this.interactables.clear()
    this.webLayers.clear()
    this.raycastTargets.length = 0
  }
}

export default XRInteractionSystem
