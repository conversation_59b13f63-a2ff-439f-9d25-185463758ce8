/**
 * DL-Engine XR模块
 * 
 * 提供WebXR支持和3D UI渲染功能
 */

// 核心类
export { default as WebXRManager, XRSessionType, XRDeviceType } from './WebXRManager'
export type { XRControllerState, XRHandState, XRSessionState } from './WebXRManager'

export { default as WebLayer3D } from './WebLayer3D'
export type { WebLayer3DConfig, WebLayer3DInteraction } from './WebLayer3D'

export { default as XRInteractionSystem, XRInteractionType } from './XRInteractionSystem'
export type { XRInteractionEvent, XRInteractable } from './XRInteractionSystem'

// UI组件
export { default as XRUIPanel, XRUIButton, XRUIInput, XRUIText, XRUIContainer } from './components/XRUIPanel'
export type { XRUIPanelProps } from './components/XRUIPanel'

// 工具函数
export * from './utils'

/**
 * XR引擎配置
 */
export interface XREngineConfig {
  /** 是否启用VR */
  enableVR: boolean
  /** 是否启用AR */
  enableAR: boolean
  /** 是否启用手部追踪 */
  enableHandTracking: boolean
  /** 默认参考空间类型 */
  referenceSpaceType: XRReferenceSpaceType
  /** 控制器模型URL */
  controllerModelUrl?: string
  /** 是否启用调试模式 */
  debug: boolean
}

/**
 * 默认XR引擎配置
 */
export const defaultXRConfig: XREngineConfig = {
  enableVR: true,
  enableAR: true,
  enableHandTracking: true,
  referenceSpaceType: 'local-floor',
  debug: false
}

/**
 * XR引擎类
 */
export class XREngine {
  private config: XREngineConfig
  private webXRManager: WebXRManager
  private interactionSystem: XRInteractionSystem | null = null
  private scene: THREE.Scene | null = null
  private camera: THREE.Camera | null = null
  
  constructor(config: Partial<XREngineConfig> = {}) {
    this.config = { ...defaultXRConfig, ...config }
    this.webXRManager = new WebXRManager()
  }
  
  /**
   * 初始化XR引擎
   */
  async initialize(scene: THREE.Scene, camera: THREE.Camera): Promise<void> {
    this.scene = scene
    this.camera = camera
    
    // 检查WebXR支持
    const support = await this.webXRManager.checkSupport()
    
    if (!support.supported) {
      throw new Error('WebXR不支持')
    }
    
    if (this.config.enableVR && !support.vr) {
      console.warn('VR不支持，已禁用VR功能')
      this.config.enableVR = false
    }
    
    if (this.config.enableAR && !support.ar) {
      console.warn('AR不支持，已禁用AR功能')
      this.config.enableAR = false
    }
    
    // 初始化交互系统
    this.interactionSystem = new XRInteractionSystem(scene, camera)
    
    // 设置事件监听器
    this.setupEventListeners()
    
    if (this.config.debug) {
      console.log('XR引擎初始化完成', { config: this.config, support })
    }
  }
  
  /**
   * 开始VR会话
   */
  async startVR(): Promise<void> {
    if (!this.config.enableVR) {
      throw new Error('VR功能未启用')
    }
    
    const sessionOptions: XRSessionInit = {
      requiredFeatures: [this.config.referenceSpaceType],
      optionalFeatures: []
    }
    
    if (this.config.enableHandTracking) {
      sessionOptions.optionalFeatures!.push('hand-tracking')
    }
    
    await this.webXRManager.startSession(XRSessionType.IMMERSIVE_VR, sessionOptions)
  }
  
  /**
   * 开始AR会话
   */
  async startAR(): Promise<void> {
    if (!this.config.enableAR) {
      throw new Error('AR功能未启用')
    }
    
    const sessionOptions: XRSessionInit = {
      requiredFeatures: [this.config.referenceSpaceType],
      optionalFeatures: ['hit-test', 'dom-overlay']
    }
    
    await this.webXRManager.startSession(XRSessionType.IMMERSIVE_AR, sessionOptions)
  }
  
  /**
   * 结束XR会话
   */
  async endSession(): Promise<void> {
    await this.webXRManager.endSession()
  }
  
  /**
   * 获取WebXR管理器
   */
  getWebXRManager(): WebXRManager {
    return this.webXRManager
  }
  
  /**
   * 获取交互系统
   */
  getInteractionSystem(): XRInteractionSystem | null {
    return this.interactionSystem
  }
  
  /**
   * 获取当前会话状态
   */
  getSessionState(): XRSessionState {
    return this.webXRManager.getSessionState()
  }
  
  /**
   * 添加可交互对象
   */
  addInteractable(object: THREE.Object3D & XRInteractable): void {
    if (this.interactionSystem) {
      this.interactionSystem.addInteractable(object)
    }
  }
  
  /**
   * 移除可交互对象
   */
  removeInteractable(object: THREE.Object3D & XRInteractable): void {
    if (this.interactionSystem) {
      this.interactionSystem.removeInteractable(object)
    }
  }
  
  /**
   * 添加Web层
   */
  addWebLayer(webLayer: WebLayer3D): void {
    if (this.scene) {
      this.scene.add(webLayer)
    }
    
    if (this.interactionSystem) {
      this.interactionSystem.addWebLayer(webLayer)
    }
  }
  
  /**
   * 移除Web层
   */
  removeWebLayer(webLayer: WebLayer3D): void {
    if (this.scene) {
      this.scene.remove(webLayer)
    }
    
    if (this.interactionSystem) {
      this.interactionSystem.removeWebLayer(webLayer)
    }
  }
  
  /**
   * 更新XR引擎
   */
  update(): void {
    if (this.interactionSystem) {
      this.interactionSystem.update()
    }
  }
  
  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.webXRManager.on('sessionstart', (sessionState) => {
      if (this.config.debug) {
        console.log('XR会话开始', sessionState)
      }
    })
    
    this.webXRManager.on('sessionend', () => {
      if (this.config.debug) {
        console.log('XR会话结束')
      }
    })
    
    this.webXRManager.on('frame', ({ frame, sessionState }) => {
      // 更新控制器
      sessionState.controllers.forEach((controller, index) => {
        if (this.interactionSystem) {
          const position = new THREE.Vector3(...controller.position)
          const rotation = new THREE.Quaternion(...controller.rotation)
          this.interactionSystem.updateController(controller.id, position, rotation, controller.buttons)
        }
      })
    })
  }
  
  /**
   * 销毁XR引擎
   */
  dispose(): void {
    if (this.interactionSystem) {
      this.interactionSystem.dispose()
      this.interactionSystem = null
    }
    
    this.webXRManager.removeAllListeners()
    this.scene = null
    this.camera = null
  }
}

// 导入THREE.js类型
import * as THREE from 'three'
import { XRInteractable } from './XRInteractionSystem'

export default XREngine
