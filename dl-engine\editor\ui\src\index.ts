/**
 * Digital Learning Engine - 编辑器UI组件库
 * 
 * 基于 Ant Design 5.0 构建的中文优先UI组件库
 * 专为数字化学习场景设计
 */

// 基础组件
export * from './components/basic'

// 复合组件
export * from './components/complex'

// 布局组件
export * from './components/layout'

// 表单组件
export * from './components/form'

// 数据展示组件
export * from './components/display'

// 反馈组件
export * from './components/feedback'

// 导航组件
export * from './components/navigation'

// 3D UI组件
export * from './components/3d'

// 主题系统
export * from './theme'

// 国际化
export * from './i18n'

// 工具函数
export * from './utils'

// 类型定义
export * from './types'

// 钩子函数
export * from './hooks'

// 上下文
export * from './contexts'

// Ant Design 重新导出（带中文配置）
export { ConfigProvider } from './providers/ConfigProvider'
export { ThemeProvider } from './providers/ThemeProvider'
export { I18nProvider } from './providers/I18nProvider'

// 常用 Ant Design 组件重新导出
export {
  Button,
  Input,
  Select,
  Checkbox,
  Radio,
  Switch,
  Slider,
  DatePicker,
  TimePicker,
  Upload,
  Form,
  Table,
  List,
  Card,
  Tabs,
  Collapse,
  Modal,
  Drawer,
  Popover,
  Tooltip,
  Dropdown,
  Menu,
  Breadcrumb,
  Pagination,
  Steps,
  Tree,
  TreeSelect,
  Cascader,
  AutoComplete,
  Transfer,
  Avatar,
  Badge,
  Tag,
  Progress,
  Spin,
  Skeleton,
  Alert,
  Message,
  Notification,
  Popconfirm,
  Result,
  Empty,
  Statistic,
  Descriptions,
  Timeline,
  Divider,
  Space,
  Affix,
  BackTop,
  Anchor,
  Row,
  Col,
  Layout
} from 'antd'

// Ant Design 图标
export * from '@ant-design/icons'
