/**
 * DL-Engine 加载组件
 * 
 * 基于 Ant Design Spin 的增强版本
 */

import React from 'react'
import { Spin, SpinProps } from 'antd'
import classNames from 'classnames'
import DLIcon from './DLIcon'

/**
 * 加载指示器类型
 */
export type DLSpinIndicator = 'default' | 'dots' | 'circle' | 'pulse' | 'custom'

/**
 * DL加载组件属性
 */
export interface DLSpinProps extends SpinProps {
  /** 加载指示器类型 */
  indicatorType?: DLSpinIndicator
  /** 自定义指示器 */
  customIndicator?: React.ReactNode
  /** 加载文本 */
  text?: string
  /** 是否全屏加载 */
  fullscreen?: boolean
  /** 自定义类名 */
  className?: string
}

/**
 * DL加载组件
 */
const DLSpin: React.FC<DLSpinProps> = ({
  indicatorType = 'default',
  customIndicator,
  text,
  fullscreen = false,
  className,
  children,
  ...props
}) => {
  /**
   * 获取加载指示器
   */
  const getIndicator = () => {
    if (customIndicator) {
      return customIndicator
    }
    
    switch (indicatorType) {
      case 'dots':
        return (
          <div className="dl-spin-dots">
            <div className="dl-spin-dot"></div>
            <div className="dl-spin-dot"></div>
            <div className="dl-spin-dot"></div>
          </div>
        )
      
      case 'circle':
        return (
          <div className="dl-spin-circle">
            <div className="dl-spin-circle-inner"></div>
          </div>
        )
      
      case 'pulse':
        return (
          <div className="dl-spin-pulse">
            <div className="dl-spin-pulse-inner"></div>
          </div>
        )
      
      case 'custom':
        return <DLIcon name="loading" className="animate-spin" />
      
      default:
        return undefined
    }
  }
  
  /**
   * 加载组件类名
   */
  const spinClassName = classNames(
    'dl-spin',
    `dl-spin--${indicatorType}`,
    {
      'dl-spin--fullscreen': fullscreen,
      'dl-spin--with-text': text
    },
    className
  )
  
  /**
   * 加载提示
   */
  const tip = text || props.tip
  
  /**
   * 全屏加载
   */
  if (fullscreen) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 flex flex-col items-center">
          <Spin
            {...props}
            indicator={getIndicator()}
            className={spinClassName}
            tip={tip}
          />
        </div>
      </div>
    )
  }
  
  return (
    <Spin
      {...props}
      indicator={getIndicator()}
      className={spinClassName}
      tip={tip}
    >
      {children}
    </Spin>
  )
}

export default DLSpin
