/**
 * DL-Engine 菜单组件
 * 
 * 基于 Ant Design Menu 的增强版本，支持多级菜单和快捷键
 */

import React, { useCallback, useMemo } from 'react'
import { Menu, MenuProps, Dropdown, DropdownProps } from 'antd'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'
import DLIcon from '../basic/DLIcon'

/**
 * 菜单项配置
 */
export interface DLMenuItemConfig {
  /** 菜单项键值 */
  key: string
  /** 菜单项标签 */
  label: string
  /** 菜单项图标 */
  icon?: string | React.ReactNode
  /** 是否禁用 */
  disabled?: boolean
  /** 是否危险操作 */
  danger?: boolean
  /** 快捷键 */
  shortcut?: string
  /** 子菜单 */
  children?: DLMenuItemConfig[]
  /** 分割线 */
  divider?: boolean
  /** 点击回调 */
  onClick?: (key: string, item: DLMenuItemConfig) => void
  /** 自定义数据 */
  data?: any
}

/**
 * DL菜单属性
 */
export interface DLMenuProps extends Omit<MenuProps, 'items' | 'onClick'> {
  /** 菜单项配置 */
  items: DLMenuItemConfig[]
  /** 是否显示快捷键 */
  showShortcuts?: boolean
  /** 菜单点击回调 */
  onClick?: (key: string, item: DLMenuItemConfig) => void
  /** 自定义类名 */
  className?: string
}

/**
 * DL下拉菜单属性
 */
export interface DLDropdownMenuProps extends Omit<DropdownProps, 'menu'> {
  /** 菜单项配置 */
  items: DLMenuItemConfig[]
  /** 是否显示快捷键 */
  showShortcuts?: boolean
  /** 菜单点击回调 */
  onClick?: (key: string, item: DLMenuItemConfig) => void
  /** 触发器 */
  children: React.ReactNode
}

/**
 * DL菜单组件
 */
const DLMenu: React.FC<DLMenuProps> = ({
  items,
  showShortcuts = true,
  onClick,
  className,
  ...props
}) => {
  const { t } = useTranslation()
  
  /**
   * 转换菜单项配置为Ant Design格式
   */
  const convertMenuItems = useCallback((menuItems: DLMenuItemConfig[]): MenuProps['items'] => {
    return menuItems.map((item) => {
      // 分割线
      if (item.divider) {
        return {
          type: 'divider',
          key: `divider-${item.key}`
        }
      }
      
      // 处理图标
      const icon = typeof item.icon === 'string' 
        ? <DLIcon name={item.icon} />
        : item.icon
      
      // 处理标签和快捷键
      const label = showShortcuts && item.shortcut ? (
        <div className="flex items-center justify-between w-full">
          <span>{item.label}</span>
          <span className="text-gray-400 text-xs ml-4">{item.shortcut}</span>
        </div>
      ) : item.label
      
      const menuItem: any = {
        key: item.key,
        label,
        icon,
        disabled: item.disabled,
        danger: item.danger,
        onClick: () => onClick?.(item.key, item)
      }
      
      // 处理子菜单
      if (item.children && item.children.length > 0) {
        menuItem.children = convertMenuItems(item.children)
        delete menuItem.onClick // 子菜单不需要点击事件
      }
      
      return menuItem
    })
  }, [showShortcuts, onClick])
  
  /**
   * 处理菜单点击
   */
  const handleClick: MenuProps['onClick'] = useCallback((info) => {
    // 查找对应的菜单项配置
    const findMenuItem = (items: DLMenuItemConfig[], key: string): DLMenuItemConfig | null => {
      for (const item of items) {
        if (item.key === key) {
          return item
        }
        if (item.children) {
          const found = findMenuItem(item.children, key)
          if (found) return found
        }
      }
      return null
    }
    
    const menuItem = findMenuItem(items, info.key)
    if (menuItem) {
      menuItem.onClick?.(info.key, menuItem)
      onClick?.(info.key, menuItem)
    }
  }, [items, onClick])
  
  /**
   * 菜单项
   */
  const menuItems = useMemo(() => convertMenuItems(items), [items, convertMenuItems])
  
  /**
   * 菜单类名
   */
  const menuClassName = classNames(
    'dl-menu',
    {
      'dl-menu--with-shortcuts': showShortcuts
    },
    className
  )
  
  return (
    <Menu
      {...props}
      className={menuClassName}
      items={menuItems}
      onClick={handleClick}
    />
  )
}

/**
 * DL下拉菜单组件
 */
export const DLDropdownMenu: React.FC<DLDropdownMenuProps> = ({
  items,
  showShortcuts = true,
  onClick,
  children,
  ...props
}) => {
  /**
   * 菜单配置
   */
  const menuProps: MenuProps = useMemo(() => ({
    items: items.map((item) => {
      // 分割线
      if (item.divider) {
        return {
          type: 'divider',
          key: `divider-${item.key}`
        }
      }
      
      // 处理图标
      const icon = typeof item.icon === 'string' 
        ? <DLIcon name={item.icon} />
        : item.icon
      
      // 处理标签和快捷键
      const label = showShortcuts && item.shortcut ? (
        <div className="flex items-center justify-between w-full min-w-32">
          <span>{item.label}</span>
          <span className="text-gray-400 text-xs ml-4">{item.shortcut}</span>
        </div>
      ) : item.label
      
      return {
        key: item.key,
        label,
        icon,
        disabled: item.disabled,
        danger: item.danger
      }
    }),
    onClick: ({ key }) => {
      const menuItem = items.find(item => item.key === key)
      if (menuItem) {
        menuItem.onClick?.(key, menuItem)
        onClick?.(key, menuItem)
      }
    }
  }), [items, showShortcuts, onClick])
  
  return (
    <Dropdown
      {...props}
      menu={menuProps}
    >
      {children}
    </Dropdown>
  )
}

/**
 * 创建上下文菜单
 */
export const createContextMenu = (
  items: DLMenuItemConfig[],
  options?: {
    showShortcuts?: boolean
    onClick?: (key: string, item: DLMenuItemConfig) => void
  }
) => {
  const { showShortcuts = true, onClick } = options || {}
  
  return (
    <DLMenu
      items={items}
      showShortcuts={showShortcuts}
      onClick={onClick}
      mode="vertical"
      selectable={false}
      className="dl-context-menu"
    />
  )
}

export default DLMenu
