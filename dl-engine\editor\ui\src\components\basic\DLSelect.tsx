/**
 * DL-Engine 选择器组件
 * 
 * 基于 Ant Design Select 的增强版本
 */

import React from 'react'
import { Select, SelectProps } from 'antd'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'
import DLIcon from './DLIcon'

/**
 * 选项配置
 */
export interface DLSelectOption {
  /** 选项值 */
  value: any
  /** 选项标签 */
  label: string
  /** 选项图标 */
  icon?: string | React.ReactNode
  /** 是否禁用 */
  disabled?: boolean
  /** 选项描述 */
  description?: string
}

/**
 * DL选择器属性
 */
export interface DLSelectProps extends Omit<SelectProps, 'options'> {
  /** 选项列表 */
  options: DLSelectOption[]
  /** 选择器标签 */
  label?: string
  /** 是否显示搜索 */
  showSearch?: boolean
  /** 是否允许清除 */
  allowClear?: boolean
  /** 验证状态 */
  status?: 'error' | 'warning' | 'success'
  /** 验证消息 */
  message?: string
  /** 自定义类名 */
  className?: string
}

/**
 * DL选择器组件
 */
const DLSelect: React.FC<DLSelectProps> = ({
  options,
  label,
  showSearch = false,
  allowClear = true,
  status,
  message,
  className,
  ...props
}) => {
  const { t } = useTranslation()
  
  /**
   * 转换选项格式
   */
  const convertedOptions = options.map(option => ({
    ...option,
    label: (
      <div className="flex items-center">
        {option.icon && (
          <span className="mr-2">
            {typeof option.icon === 'string' ? (
              <DLIcon name={option.icon} />
            ) : (
              option.icon
            )}
          </span>
        )}
        <div>
          <div>{option.label}</div>
          {option.description && (
            <div className="text-xs text-gray-500">{option.description}</div>
          )}
        </div>
      </div>
    )
  }))
  
  /**
   * 选择器类名
   */
  const selectClassName = classNames(
    'dl-select',
    {
      'dl-select--with-label': label,
      'dl-select--error': status === 'error',
      'dl-select--warning': status === 'warning',
      'dl-select--success': status === 'success'
    },
    className
  )
  
  return (
    <div className={selectClassName}>
      {/* 标签 */}
      {label && (
        <div className="dl-select-label mb-1">
          <span className="text-sm text-gray-700">{label}</span>
        </div>
      )}
      
      {/* 选择器 */}
      <Select
        {...props}
        options={convertedOptions}
        showSearch={showSearch}
        allowClear={allowClear}
        status={status}
        optionFilterProp="children"
        filterOption={(input, option) =>
          (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
        }
      />
      
      {/* 验证消息 */}
      {message && (
        <div className={`dl-select-message mt-1 text-xs ${
          status === 'error' ? 'text-red-500' :
          status === 'warning' ? 'text-yellow-500' :
          status === 'success' ? 'text-green-500' :
          'text-gray-500'
        }`}>
          {message}
        </div>
      )}
    </div>
  )
}

export default DLSelect
