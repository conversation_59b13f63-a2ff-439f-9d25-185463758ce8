/**
 * XR UI面板组件
 * 
 * 在3D空间中创建可交互的UI面板
 */

import { Object3D, PlaneGeometry, MeshBasicMaterial, Mesh, Vector3, Quaternion } from 'three'
import { WebLayer3D } from './WebLayer3D'

export interface XRUIPanelOptions {
  /** 面板宽度（米） */
  width?: number
  /** 面板高度（米） */
  height?: number
  /** 面板位置 */
  position?: Vector3
  /** 面板旋转 */
  rotation?: Quaternion
  /** 面板标题 */
  title?: string
  /** 是否可拖拽 */
  draggable?: boolean
  /** 是否可调整大小 */
  resizable?: boolean
  /** 背景颜色 */
  backgroundColor?: string
  /** 边框颜色 */
  borderColor?: string
  /** 透明度 */
  opacity?: number
  /** 是否显示关闭按钮 */
  closable?: boolean
  /** 关闭回调 */
  onClose?: () => void
}

/**
 * XR UI面板类
 */
export class XRUIPanel extends Object3D {
  private options: Required<XRUIPanelOptions>
  private webLayer: WebLayer3D
  private panelElement: HTMLDivElement
  private titleElement: HTMLDivElement
  private contentElement: HTMLDivElement
  private closeButton?: HTMLButtonElement
  private isDragging = false
  private dragOffset = new Vector3()

  constructor(options: XRUIPanelOptions = {}) {
    super()

    // 设置默认选项
    this.options = {
      width: 1.0,
      height: 0.8,
      position: new Vector3(0, 0, 0),
      rotation: new Quaternion(),
      title: 'XR Panel',
      draggable: true,
      resizable: false,
      backgroundColor: '#2d2d30',
      borderColor: '#3c3c3c',
      opacity: 0.9,
      closable: true,
      onClose: () => {},
      ...options
    }

    this.createPanel()
    this.setupInteraction()
    this.updateTransform()
  }

  /**
   * 创建面板DOM结构
   */
  private createPanel(): void {
    // 创建主面板元素
    this.panelElement = document.createElement('div')
    this.panelElement.style.cssText = `
      width: 100%;
      height: 100%;
      background-color: ${this.options.backgroundColor};
      border: 2px solid ${this.options.borderColor};
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      color: white;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    `

    // 创建标题栏
    this.titleElement = document.createElement('div')
    this.titleElement.style.cssText = `
      background-color: #1e1e1e;
      padding: 12px 16px;
      border-bottom: 1px solid ${this.options.borderColor};
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: ${this.options.draggable ? 'move' : 'default'};
      user-select: none;
      font-size: 14px;
      font-weight: 500;
    `
    this.titleElement.textContent = this.options.title

    // 创建关闭按钮
    if (this.options.closable) {
      this.closeButton = document.createElement('button')
      this.closeButton.style.cssText = `
        background: none;
        border: none;
        color: #cccccc;
        font-size: 16px;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background-color 0.2s;
      `
      this.closeButton.innerHTML = '×'
      this.closeButton.addEventListener('click', () => {
        this.options.onClose()
      })
      this.closeButton.addEventListener('mouseenter', () => {
        this.closeButton!.style.backgroundColor = '#ff4444'
      })
      this.closeButton.addEventListener('mouseleave', () => {
        this.closeButton!.style.backgroundColor = 'transparent'
      })
      this.titleElement.appendChild(this.closeButton)
    }

    // 创建内容区域
    this.contentElement = document.createElement('div')
    this.contentElement.style.cssText = `
      flex: 1;
      padding: 16px;
      overflow: auto;
      background-color: ${this.options.backgroundColor};
    `

    // 组装面板
    this.panelElement.appendChild(this.titleElement)
    this.panelElement.appendChild(this.contentElement)

    // 创建WebLayer3D
    this.webLayer = new WebLayer3D(this.panelElement, {
      pixelWidth: 1024,
      pixelHeight: Math.round(1024 * (this.options.height / this.options.width))
    })

    this.webLayer.scale.set(this.options.width, this.options.height, 1)
    this.add(this.webLayer)
  }

  /**
   * 设置交互功能
   */
  private setupInteraction(): void {
    if (this.options.draggable) {
      this.titleElement.addEventListener('mousedown', this.onDragStart.bind(this))
      document.addEventListener('mousemove', this.onDragMove.bind(this))
      document.addEventListener('mouseup', this.onDragEnd.bind(this))
    }
  }

  /**
   * 开始拖拽
   */
  private onDragStart(event: MouseEvent): void {
    this.isDragging = true
    // 这里应该计算拖拽偏移量
    // 暂时使用简单实现
    event.preventDefault()
  }

  /**
   * 拖拽移动
   */
  private onDragMove(event: MouseEvent): void {
    if (!this.isDragging) return
    
    // 这里应该实现实际的拖拽逻辑
    // 需要将鼠标坐标转换为3D空间坐标
    // 暂时使用简单实现
  }

  /**
   * 结束拖拽
   */
  private onDragEnd(event: MouseEvent): void {
    this.isDragging = false
  }

  /**
   * 更新变换
   */
  private updateTransform(): void {
    this.position.copy(this.options.position)
    this.quaternion.copy(this.options.rotation)
  }

  /**
   * 设置面板内容
   */
  setContent(content: string | HTMLElement): void {
    if (typeof content === 'string') {
      this.contentElement.innerHTML = content
    } else {
      this.contentElement.innerHTML = ''
      this.contentElement.appendChild(content)
    }
  }

  /**
   * 获取内容元素
   */
  getContentElement(): HTMLDivElement {
    return this.contentElement
  }

  /**
   * 设置面板标题
   */
  setTitle(title: string): void {
    this.options.title = title
    const titleText = this.titleElement.childNodes[0]
    if (titleText) {
      titleText.textContent = title
    }
  }

  /**
   * 设置面板位置
   */
  setPosition(position: Vector3): void {
    this.options.position.copy(position)
    this.updateTransform()
  }

  /**
   * 设置面板旋转
   */
  setRotation(rotation: Quaternion): void {
    this.options.rotation.copy(rotation)
    this.updateTransform()
  }

  /**
   * 设置面板大小
   */
  setSize(width: number, height: number): void {
    this.options.width = width
    this.options.height = height
    this.webLayer.scale.set(width, height, 1)
  }

  /**
   * 显示面板
   */
  show(): void {
    this.visible = true
  }

  /**
   * 隐藏面板
   */
  hide(): void {
    this.visible = false
  }

  /**
   * 销毁面板
   */
  dispose(): void {
    if (this.webLayer) {
      this.webLayer.dispose()
    }
    if (this.parent) {
      this.parent.remove(this)
    }
  }
}

export default XRUIPanel
