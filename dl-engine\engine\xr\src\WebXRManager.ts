/**
 * WebXR管理器
 * 
 * 管理WebXR会话和设备交互
 */

import { EventEmitter } from 'events'

/**
 * XR会话类型
 */
export enum XRSessionType {
  INLINE = 'inline',
  IMMERSIVE_VR = 'immersive-vr',
  IMMERSIVE_AR = 'immersive-ar'
}

/**
 * XR设备类型
 */
export enum XRDeviceType {
  UNKNOWN = 'unknown',
  VR_HEADSET = 'vr-headset',
  AR_GLASSES = 'ar-glasses',
  MOBILE = 'mobile',
  DESKTOP = 'desktop'
}

/**
 * XR控制器状态
 */
export interface XRControllerState {
  id: string
  connected: boolean
  position: [number, number, number]
  rotation: [number, number, number, number]
  buttons: boolean[]
  axes: number[]
  hapticActuators: GamepadHapticActuator[]
}

/**
 * XR手部追踪状态
 */
export interface XRHandState {
  id: string
  connected: boolean
  joints: {
    [jointName: string]: {
      position: [number, number, number]
      rotation: [number, number, number, number]
      radius: number
    }
  }
}

/**
 * XR会话状态
 */
export interface XRSessionState {
  active: boolean
  type: XRSessionType
  deviceType: XRDeviceType
  controllers: XRControllerState[]
  hands: XRHandState[]
  viewerPose: {
    position: [number, number, number]
    rotation: [number, number, number, number]
  } | null
}

/**
 * WebXR管理器类
 */
export class WebXRManager extends EventEmitter {
  private session: XRSession | null = null
  private referenceSpace: XRReferenceSpace | null = null
  private animationFrameId: number | null = null
  private sessionState: XRSessionState
  
  constructor() {
    super()
    
    this.sessionState = {
      active: false,
      type: XRSessionType.INLINE,
      deviceType: XRDeviceType.UNKNOWN,
      controllers: [],
      hands: [],
      viewerPose: null
    }
  }
  
  /**
   * 检查WebXR支持
   */
  async checkSupport(): Promise<{
    supported: boolean
    vr: boolean
    ar: boolean
    handTracking: boolean
  }> {
    if (!navigator.xr) {
      return {
        supported: false,
        vr: false,
        ar: false,
        handTracking: false
      }
    }
    
    try {
      const [vrSupported, arSupported] = await Promise.all([
        navigator.xr.isSessionSupported('immersive-vr'),
        navigator.xr.isSessionSupported('immersive-ar')
      ])
      
      // 检查手部追踪支持（如果可用）
      let handTracking = false
      try {
        // @ts-ignore - 手部追踪API可能不在所有类型定义中
        handTracking = await navigator.xr.isSessionSupported('immersive-vr', {
          requiredFeatures: ['hand-tracking']
        })
      } catch (e) {
        // 手部追踪不支持
      }
      
      return {
        supported: true,
        vr: vrSupported,
        ar: arSupported,
        handTracking
      }
    } catch (error) {
      console.error('检查WebXR支持时出错:', error)
      return {
        supported: false,
        vr: false,
        ar: false,
        handTracking: false
      }
    }
  }
  
  /**
   * 开始XR会话
   */
  async startSession(type: XRSessionType, options?: XRSessionInit): Promise<void> {
    if (!navigator.xr) {
      throw new Error('WebXR不支持')
    }
    
    try {
      const sessionOptions: XRSessionInit = {
        requiredFeatures: ['local-floor'],
        optionalFeatures: ['hand-tracking', 'hit-test'],
        ...options
      }
      
      this.session = await navigator.xr.requestSession(type, sessionOptions)
      
      // 设置参考空间
      this.referenceSpace = await this.session.requestReferenceSpace('local-floor')
      
      // 设置会话事件监听器
      this.session.addEventListener('end', this.handleSessionEnd.bind(this))
      this.session.addEventListener('inputsourceschange', this.handleInputSourcesChange.bind(this))
      
      // 更新会话状态
      this.sessionState.active = true
      this.sessionState.type = type
      this.sessionState.deviceType = this.detectDeviceType()
      
      // 开始渲染循环
      this.startRenderLoop()
      
      this.emit('sessionstart', this.sessionState)
      
    } catch (error) {
      console.error('启动XR会话失败:', error)
      throw error
    }
  }
  
  /**
   * 结束XR会话
   */
  async endSession(): Promise<void> {
    if (this.session) {
      await this.session.end()
    }
  }
  
  /**
   * 获取当前会话状态
   */
  getSessionState(): XRSessionState {
    return { ...this.sessionState }
  }
  
  /**
   * 检测设备类型
   */
  private detectDeviceType(): XRDeviceType {
    // 基于用户代理和可用功能检测设备类型
    const userAgent = navigator.userAgent.toLowerCase()
    
    if (userAgent.includes('oculus') || userAgent.includes('quest')) {
      return XRDeviceType.VR_HEADSET
    }
    
    if (userAgent.includes('mobile') || userAgent.includes('android') || userAgent.includes('iphone')) {
      return XRDeviceType.MOBILE
    }
    
    return XRDeviceType.DESKTOP
  }
  
  /**
   * 处理会话结束
   */
  private handleSessionEnd(): void {
    this.session = null
    this.referenceSpace = null
    
    if (this.animationFrameId) {
      this.session?.cancelAnimationFrame(this.animationFrameId)
      this.animationFrameId = null
    }
    
    this.sessionState.active = false
    this.sessionState.controllers = []
    this.sessionState.hands = []
    this.sessionState.viewerPose = null
    
    this.emit('sessionend')
  }
  
  /**
   * 处理输入源变化
   */
  private handleInputSourcesChange(event: XRInputSourceChangeEvent): void {
    this.updateControllers()
    this.emit('inputsourceschange', event)
  }
  
  /**
   * 更新控制器状态
   */
  private updateControllers(): void {
    if (!this.session) return
    
    const controllers: XRControllerState[] = []
    
    for (const inputSource of this.session.inputSources) {
      if (inputSource.gamepad) {
        const controller: XRControllerState = {
          id: inputSource.gamepad.id,
          connected: inputSource.gamepad.connected,
          position: [0, 0, 0],
          rotation: [0, 0, 0, 1],
          buttons: inputSource.gamepad.buttons.map(button => button.pressed),
          axes: Array.from(inputSource.gamepad.axes),
          hapticActuators: inputSource.gamepad.hapticActuators || []
        }
        
        // 获取控制器姿态
        if (inputSource.gripSpace && this.referenceSpace) {
          // 这里需要在渲染循环中更新姿态
        }
        
        controllers.push(controller)
      }
    }
    
    this.sessionState.controllers = controllers
  }
  
  /**
   * 开始渲染循环
   */
  private startRenderLoop(): void {
    if (!this.session) return
    
    const renderFrame = (time: number, frame: XRFrame) => {
      if (!this.session || !this.referenceSpace) return
      
      // 更新查看器姿态
      const viewerPose = frame.getViewerPose(this.referenceSpace)
      if (viewerPose) {
        this.sessionState.viewerPose = {
          position: [
            viewerPose.transform.position.x,
            viewerPose.transform.position.y,
            viewerPose.transform.position.z
          ],
          rotation: [
            viewerPose.transform.orientation.x,
            viewerPose.transform.orientation.y,
            viewerPose.transform.orientation.z,
            viewerPose.transform.orientation.w
          ]
        }
      }
      
      // 更新控制器姿态
      this.updateControllerPoses(frame)
      
      // 更新手部追踪
      this.updateHandTracking(frame)
      
      // 发出帧更新事件
      this.emit('frame', { time, frame, sessionState: this.sessionState })
      
      // 请求下一帧
      this.animationFrameId = this.session.requestAnimationFrame(renderFrame)
    }
    
    this.animationFrameId = this.session.requestAnimationFrame(renderFrame)
  }
  
  /**
   * 更新控制器姿态
   */
  private updateControllerPoses(frame: XRFrame): void {
    if (!this.session || !this.referenceSpace) return
    
    for (let i = 0; i < this.session.inputSources.length; i++) {
      const inputSource = this.session.inputSources[i]
      const controller = this.sessionState.controllers[i]
      
      if (controller && inputSource.gripSpace) {
        const pose = frame.getPose(inputSource.gripSpace, this.referenceSpace)
        if (pose) {
          controller.position = [
            pose.transform.position.x,
            pose.transform.position.y,
            pose.transform.position.z
          ]
          controller.rotation = [
            pose.transform.orientation.x,
            pose.transform.orientation.y,
            pose.transform.orientation.z,
            pose.transform.orientation.w
          ]
        }
      }
    }
  }
  
  /**
   * 更新手部追踪
   */
  private updateHandTracking(frame: XRFrame): void {
    // TODO: 实现手部追踪更新
    // 这需要WebXR手部追踪API的支持
  }
  
  /**
   * 触发控制器震动
   */
  async vibrateController(controllerId: string, intensity: number, duration: number): Promise<void> {
    const controller = this.sessionState.controllers.find(c => c.id === controllerId)
    if (controller && controller.hapticActuators.length > 0) {
      try {
        await controller.hapticActuators[0].pulse(intensity, duration)
      } catch (error) {
        console.warn('控制器震动失败:', error)
      }
    }
  }
}

export default WebXRManager
