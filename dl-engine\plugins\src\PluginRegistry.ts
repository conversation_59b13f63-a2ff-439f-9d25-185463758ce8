/**
 * 插件注册表
 * 
 * 管理插件的注册信息和配置
 */

import { PluginMetadata, PluginConfig, PluginType } from './PluginAPI'

/**
 * 注册表条目
 */
export interface RegistryEntry {
  metadata: PluginMetadata
  path: string
  config: PluginConfig
  registeredAt: number
  lastUpdated: number
}

/**
 * 插件搜索过滤器
 */
export interface PluginFilter {
  type?: PluginType
  enabled?: boolean
  author?: string
  keyword?: string
  version?: string
}

/**
 * 插件注册表类
 */
export class PluginRegistry {
  private registry: Map<string, RegistryEntry> = new Map()
  private storageKey = 'dl-engine-plugin-registry'
  
  /**
   * 初始化注册表
   */
  async initialize(): Promise<void> {
    await this.loadFromStorage()
  }
  
  /**
   * 注册插件
   */
  async register(metadata: PluginMetadata, pluginPath: string): Promise<void> {
    // 验证插件ID唯一性
    if (this.registry.has(metadata.id)) {
      const existing = this.registry.get(metadata.id)!
      if (existing.metadata.version !== metadata.version) {
        console.warn(`插件 ${metadata.id} 已存在，版本从 ${existing.metadata.version} 更新到 ${metadata.version}`)
      }
    }
    
    // 创建默认配置
    const defaultConfig: PluginConfig = {
      enabled: false,
      settings: {},
      keybindings: {},
      ui: {
        position: 'right',
        size: { width: 300, height: 400 },
        visible: true
      }
    }
    
    // 获取现有配置（如果有）
    const existingEntry = this.registry.get(metadata.id)
    const config = existingEntry ? existingEntry.config : defaultConfig
    
    const entry: RegistryEntry = {
      metadata,
      path: pluginPath,
      config,
      registeredAt: existingEntry?.registeredAt || Date.now(),
      lastUpdated: Date.now()
    }
    
    this.registry.set(metadata.id, entry)
    await this.saveToStorage()
  }
  
  /**
   * 取消注册插件
   */
  async unregister(pluginId: string): Promise<void> {
    if (this.registry.delete(pluginId)) {
      await this.saveToStorage()
    }
  }
  
  /**
   * 获取插件信息
   */
  async get(pluginId: string): Promise<RegistryEntry | null> {
    return this.registry.get(pluginId) || null
  }
  
  /**
   * 获取所有插件
   */
  async getAll(): Promise<RegistryEntry[]> {
    return Array.from(this.registry.values())
  }
  
  /**
   * 获取已启用的插件
   */
  async getEnabledPlugins(): Promise<PluginMetadata[]> {
    const enabled: PluginMetadata[] = []
    
    for (const entry of this.registry.values()) {
      if (entry.config.enabled) {
        enabled.push(entry.metadata)
      }
    }
    
    return enabled
  }
  
  /**
   * 搜索插件
   */
  async search(filter: PluginFilter): Promise<RegistryEntry[]> {
    const results: RegistryEntry[] = []
    
    for (const entry of this.registry.values()) {
      if (this.matchesFilter(entry, filter)) {
        results.push(entry)
      }
    }
    
    return results
  }
  
  /**
   * 检查过滤器匹配
   */
  private matchesFilter(entry: RegistryEntry, filter: PluginFilter): boolean {
    // 类型过滤
    if (filter.type && entry.metadata.type !== filter.type) {
      return false
    }
    
    // 启用状态过滤
    if (filter.enabled !== undefined && entry.config.enabled !== filter.enabled) {
      return false
    }
    
    // 作者过滤
    if (filter.author && entry.metadata.author !== filter.author) {
      return false
    }
    
    // 版本过滤
    if (filter.version && entry.metadata.version !== filter.version) {
      return false
    }
    
    // 关键词过滤
    if (filter.keyword) {
      const keyword = filter.keyword.toLowerCase()
      const searchText = [
        entry.metadata.name,
        entry.metadata.description,
        ...(entry.metadata.keywords || [])
      ].join(' ').toLowerCase()
      
      if (!searchText.includes(keyword)) {
        return false
      }
    }
    
    return true
  }
  
  /**
   * 获取插件配置
   */
  async getPluginConfig(pluginId: string): Promise<PluginConfig> {
    const entry = this.registry.get(pluginId)
    if (!entry) {
      throw new Error(`插件 ${pluginId} 未注册`)
    }
    
    return { ...entry.config }
  }
  
  /**
   * 设置插件配置
   */
  async setPluginConfig(pluginId: string, config: PluginConfig): Promise<void> {
    const entry = this.registry.get(pluginId)
    if (!entry) {
      throw new Error(`插件 ${pluginId} 未注册`)
    }
    
    entry.config = { ...config }
    entry.lastUpdated = Date.now()
    
    await this.saveToStorage()
  }
  
  /**
   * 启用插件
   */
  async enablePlugin(pluginId: string): Promise<void> {
    const entry = this.registry.get(pluginId)
    if (!entry) {
      throw new Error(`插件 ${pluginId} 未注册`)
    }
    
    entry.config.enabled = true
    entry.lastUpdated = Date.now()
    
    await this.saveToStorage()
  }
  
  /**
   * 禁用插件
   */
  async disablePlugin(pluginId: string): Promise<void> {
    const entry = this.registry.get(pluginId)
    if (!entry) {
      throw new Error(`插件 ${pluginId} 未注册`)
    }
    
    entry.config.enabled = false
    entry.lastUpdated = Date.now()
    
    await this.saveToStorage()
  }
  
  /**
   * 更新插件设置
   */
  async updatePluginSettings(pluginId: string, settings: Record<string, any>): Promise<void> {
    const entry = this.registry.get(pluginId)
    if (!entry) {
      throw new Error(`插件 ${pluginId} 未注册`)
    }
    
    entry.config.settings = { ...entry.config.settings, ...settings }
    entry.lastUpdated = Date.now()
    
    await this.saveToStorage()
  }
  
  /**
   * 获取插件统计信息
   */
  async getStatistics(): Promise<{
    total: number
    enabled: number
    byType: Record<PluginType, number>
    byAuthor: Record<string, number>
  }> {
    const stats = {
      total: this.registry.size,
      enabled: 0,
      byType: {} as Record<PluginType, number>,
      byAuthor: {} as Record<string, number>
    }
    
    for (const entry of this.registry.values()) {
      // 统计启用数量
      if (entry.config.enabled) {
        stats.enabled++
      }
      
      // 按类型统计
      const type = entry.metadata.type
      stats.byType[type] = (stats.byType[type] || 0) + 1
      
      // 按作者统计
      const author = entry.metadata.author
      stats.byAuthor[author] = (stats.byAuthor[author] || 0) + 1
    }
    
    return stats
  }
  
  /**
   * 检查插件依赖
   */
  async checkDependencies(pluginId: string): Promise<{
    satisfied: boolean
    missing: string[]
    conflicts: string[]
  }> {
    const entry = this.registry.get(pluginId)
    if (!entry) {
      throw new Error(`插件 ${pluginId} 未注册`)
    }
    
    const result = {
      satisfied: true,
      missing: [] as string[],
      conflicts: [] as string[]
    }
    
    if (entry.metadata.dependencies) {
      for (const [depId, requiredVersion] of Object.entries(entry.metadata.dependencies)) {
        const depEntry = this.registry.get(depId)
        
        if (!depEntry) {
          result.missing.push(depId)
          result.satisfied = false
        } else {
          // 简化的版本检查
          if (!this.isVersionCompatible(depEntry.metadata.version, requiredVersion)) {
            result.conflicts.push(`${depId}: 需要 ${requiredVersion}, 当前 ${depEntry.metadata.version}`)
            result.satisfied = false
          }
        }
      }
    }
    
    return result
  }
  
  /**
   * 检查版本兼容性
   */
  private isVersionCompatible(currentVersion: string, requiredVersion: string): boolean {
    // 简化的版本比较，实际应该使用 semver 库
    return currentVersion === requiredVersion || requiredVersion === '*'
  }
  
  /**
   * 导出注册表
   */
  async export(): Promise<string> {
    const data = {
      version: '1.0.0',
      timestamp: Date.now(),
      plugins: Array.from(this.registry.entries()).map(([id, entry]) => ({
        id,
        ...entry
      }))
    }
    
    return JSON.stringify(data, null, 2)
  }
  
  /**
   * 导入注册表
   */
  async import(data: string): Promise<void> {
    try {
      const parsed = JSON.parse(data)
      
      if (!parsed.plugins || !Array.isArray(parsed.plugins)) {
        throw new Error('无效的注册表数据格式')
      }
      
      for (const pluginData of parsed.plugins) {
        const { id, ...entry } = pluginData
        this.registry.set(id, entry)
      }
      
      await this.saveToStorage()
      
    } catch (error) {
      throw new Error(`导入注册表失败: ${error.message}`)
    }
  }
  
  /**
   * 从存储加载
   */
  private async loadFromStorage(): Promise<void> {
    try {
      const data = localStorage.getItem(this.storageKey)
      if (data) {
        const parsed = JSON.parse(data)
        this.registry = new Map(Object.entries(parsed))
      }
    } catch (error) {
      console.warn('加载插件注册表失败:', error)
    }
  }
  
  /**
   * 保存到存储
   */
  private async saveToStorage(): Promise<void> {
    try {
      const data = Object.fromEntries(this.registry)
      localStorage.setItem(this.storageKey, JSON.stringify(data))
    } catch (error) {
      console.error('保存插件注册表失败:', error)
    }
  }
  
  /**
   * 清空注册表
   */
  async clear(): Promise<void> {
    this.registry.clear()
    await this.saveToStorage()
  }
}

export default PluginRegistry
