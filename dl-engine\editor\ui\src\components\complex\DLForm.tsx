/**
 * DL-Engine 表单组件
 * 
 * 基于 Ant Design Form 的增强版本，支持动态表单和验证
 */

import React, { useCallback, useImperativeHandle, forwardRef } from 'react'
import { Form, FormProps, FormInstance, Button, Space, Card, Divider } from 'antd'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'
import DLFormItem from './DLFormItem'

/**
 * 表单字段配置
 */
export interface DLFormFieldConfig {
  /** 字段名 */
  name: string
  /** 字段标签 */
  label: string
  /** 字段类型 */
  type: 'input' | 'textarea' | 'number' | 'select' | 'switch' | 'slider' | 'color' | 'date' | 'upload' | 'custom'
  /** 是否必填 */
  required?: boolean
  /** 验证规则 */
  rules?: any[]
  /** 字段选项（用于select等） */
  options?: Array<{ label: string; value: any }>
  /** 占位符 */
  placeholder?: string
  /** 默认值 */
  defaultValue?: any
  /** 是否禁用 */
  disabled?: boolean
  /** 是否隐藏 */
  hidden?: boolean
  /** 字段描述 */
  description?: string
  /** 自定义渲染 */
  render?: (value: any, onChange: (value: any) => void, field: DLFormFieldConfig) => React.ReactNode
  /** 字段属性 */
  props?: Record<string, any>
  /** 依赖字段 */
  dependencies?: string[]
  /** 显示条件 */
  condition?: (values: Record<string, any>) => boolean
}

/**
 * 表单分组配置
 */
export interface DLFormGroupConfig {
  /** 分组标题 */
  title: string
  /** 分组描述 */
  description?: string
  /** 分组字段 */
  fields: DLFormFieldConfig[]
  /** 是否可折叠 */
  collapsible?: boolean
  /** 默认是否展开 */
  defaultExpanded?: boolean
}

/**
 * DL表单属性
 */
export interface DLFormProps extends Omit<FormProps, 'onFinish'> {
  /** 表单字段配置 */
  fields?: DLFormFieldConfig[]
  /** 表单分组配置 */
  groups?: DLFormGroupConfig[]
  /** 是否显示提交按钮 */
  showSubmit?: boolean
  /** 是否显示重置按钮 */
  showReset?: boolean
  /** 是否显示取消按钮 */
  showCancel?: boolean
  /** 提交按钮文本 */
  submitText?: string
  /** 重置按钮文本 */
  resetText?: string
  /** 取消按钮文本 */
  cancelText?: string
  /** 是否加载中 */
  loading?: boolean
  /** 表单提交回调 */
  onSubmit?: (values: Record<string, any>) => void | Promise<void>
  /** 表单重置回调 */
  onReset?: () => void
  /** 表单取消回调 */
  onCancel?: () => void
  /** 字段值变化回调 */
  onFieldChange?: (field: string, value: any, allValues: Record<string, any>) => void
  /** 自定义类名 */
  className?: string
}

/**
 * 表单引用类型
 */
export interface DLFormRef {
  form: FormInstance
  submit: () => void
  reset: () => void
  getValues: () => Record<string, any>
  setValues: (values: Record<string, any>) => void
  validateFields: () => Promise<Record<string, any>>
}

/**
 * DL表单组件
 */
const DLForm = forwardRef<DLFormRef, DLFormProps>(({
  fields = [],
  groups = [],
  showSubmit = true,
  showReset = false,
  showCancel = false,
  submitText,
  resetText,
  cancelText,
  loading = false,
  onSubmit,
  onReset,
  onCancel,
  onFieldChange,
  className,
  ...props
}, ref) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  
  /**
   * 暴露表单方法
   */
  useImperativeHandle(ref, () => ({
    form,
    submit: () => form.submit(),
    reset: () => form.resetFields(),
    getValues: () => form.getFieldsValue(),
    setValues: (values) => form.setFieldsValue(values),
    validateFields: () => form.validateFields()
  }), [form])
  
  /**
   * 处理表单提交
   */
  const handleFinish = useCallback(async (values: Record<string, any>) => {
    try {
      await onSubmit?.(values)
    } catch (error) {
      console.error('表单提交失败:', error)
    }
  }, [onSubmit])
  
  /**
   * 处理表单重置
   */
  const handleReset = useCallback(() => {
    form.resetFields()
    onReset?.()
  }, [form, onReset])
  
  /**
   * 处理字段值变化
   */
  const handleValuesChange = useCallback((changedValues: Record<string, any>, allValues: Record<string, any>) => {
    const changedField = Object.keys(changedValues)[0]
    if (changedField) {
      onFieldChange?.(changedField, changedValues[changedField], allValues)
    }
  }, [onFieldChange])
  
  /**
   * 渲染表单字段
   */
  const renderField = useCallback((field: DLFormFieldConfig, values: Record<string, any> = {}) => {
    // 检查显示条件
    if (field.condition && !field.condition(values)) {
      return null
    }
    
    if (field.hidden) {
      return null
    }
    
    return (
      <DLFormItem
        key={field.name}
        field={field}
        form={form}
      />
    )
  }, [form])
  
  /**
   * 渲染表单分组
   */
  const renderGroup = useCallback((group: DLFormGroupConfig, values: Record<string, any> = {}) => {
    const visibleFields = group.fields.filter(field => 
      !field.hidden && (!field.condition || field.condition(values))
    )
    
    if (visibleFields.length === 0) {
      return null
    }
    
    return (
      <Card
        key={group.title}
        title={group.title}
        size="small"
        className="dl-form-group"
        extra={group.description && (
          <span className="text-gray-500 text-sm">{group.description}</span>
        )}
      >
        {visibleFields.map(field => renderField(field, values))}
      </Card>
    )
  }, [renderField])
  
  /**
   * 表单类名
   */
  const formClassName = classNames(
    'dl-form',
    {
      'dl-form--loading': loading,
      'dl-form--grouped': groups.length > 0
    },
    className
  )
  
  return (
    <Form
      {...props}
      form={form}
      className={formClassName}
      layout="vertical"
      onFinish={handleFinish}
      onValuesChange={handleValuesChange}
    >
      <Form.Item noStyle shouldUpdate>
        {({ getFieldsValue }) => {
          const values = getFieldsValue()
          
          return (
            <>
              {/* 渲染分组表单 */}
              {groups.length > 0 && (
                <div className="dl-form-groups">
                  {groups.map(group => renderGroup(group, values))}
                </div>
              )}
              
              {/* 渲染普通字段 */}
              {fields.length > 0 && (
                <div className="dl-form-fields">
                  {fields.map(field => renderField(field, values))}
                </div>
              )}
              
              {/* 表单操作按钮 */}
              {(showSubmit || showReset || showCancel) && (
                <>
                  <Divider />
                  <div className="dl-form-actions">
                    <Space>
                      {showSubmit && (
                        <Button
                          type="primary"
                          htmlType="submit"
                          loading={loading}
                        >
                          {submitText || t('common.submit')}
                        </Button>
                      )}
                      
                      {showReset && (
                        <Button
                          onClick={handleReset}
                          disabled={loading}
                        >
                          {resetText || t('common.reset')}
                        </Button>
                      )}
                      
                      {showCancel && (
                        <Button
                          onClick={onCancel}
                          disabled={loading}
                        >
                          {cancelText || t('common.cancel')}
                        </Button>
                      )}
                    </Space>
                  </div>
                </>
              )}
            </>
          )
        }}
      </Form.Item>
    </Form>
  )
})

DLForm.displayName = 'DLForm'

export default DLForm
