/**
 * 可视化脚本调试系统
 * 
 * 提供断点、单步执行、变量监视等调试功能
 */

import { NodeInstance, ExecutionContext } from '../types'
import { ScriptExecutor } from './ScriptExecutor'

export interface Breakpoint {
  id: string
  nodeId: string
  enabled: boolean
  condition?: string
  hitCount: number
  maxHits?: number
}

export interface DebugState {
  isDebugging: boolean
  isPaused: boolean
  currentNodeId?: string
  executionStack: string[]
  variables: Map<string, any>
  breakpoints: Map<string, Breakpoint>
}

export interface WatchExpression {
  id: string
  expression: string
  value?: any
  error?: string
}

/**
 * 调试系统类
 */
export class DebugSystem {
  private executor: ScriptExecutor
  private state: DebugState
  private watchExpressions: Map<string, WatchExpression> = new Map()
  private stepMode: 'none' | 'into' | 'over' | 'out' = 'none'
  private debugCallbacks: {
    onBreakpoint?: (nodeId: string) => void
    onStep?: (nodeId: string) => void
    onVariableChange?: (name: string, value: any) => void
    onError?: (error: string) => void
  } = {}

  constructor(executor: ScriptExecutor) {
    this.executor = executor
    this.state = {
      isDebugging: false,
      isPaused: false,
      executionStack: [],
      variables: new Map(),
      breakpoints: new Map()
    }
  }

  /**
   * 开始调试
   */
  startDebugging(): void {
    this.state.isDebugging = true
    this.state.isPaused = false
    this.state.currentNodeId = undefined
    this.state.executionStack = []
    this.stepMode = 'none'
  }

  /**
   * 停止调试
   */
  stopDebugging(): void {
    this.state.isDebugging = false
    this.state.isPaused = false
    this.state.currentNodeId = undefined
    this.state.executionStack = []
    this.stepMode = 'none'
    this.executor.stop()
  }

  /**
   * 添加断点
   */
  addBreakpoint(nodeId: string, condition?: string): string {
    const id = `bp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const breakpoint: Breakpoint = {
      id,
      nodeId,
      enabled: true,
      condition,
      hitCount: 0
    }
    this.state.breakpoints.set(id, breakpoint)
    return id
  }

  /**
   * 移除断点
   */
  removeBreakpoint(breakpointId: string): void {
    this.state.breakpoints.delete(breakpointId)
  }

  /**
   * 切换断点启用状态
   */
  toggleBreakpoint(breakpointId: string): void {
    const breakpoint = this.state.breakpoints.get(breakpointId)
    if (breakpoint) {
      breakpoint.enabled = !breakpoint.enabled
    }
  }

  /**
   * 清除所有断点
   */
  clearAllBreakpoints(): void {
    this.state.breakpoints.clear()
  }

  /**
   * 检查是否应该在节点处暂停
   */
  shouldPauseAtNode(nodeId: string, context: ExecutionContext): boolean {
    if (!this.state.isDebugging) return false

    // 检查单步模式
    if (this.stepMode !== 'none') {
      this.stepMode = 'none'
      return true
    }

    // 检查断点
    for (const breakpoint of this.state.breakpoints.values()) {
      if (breakpoint.nodeId === nodeId && breakpoint.enabled) {
        breakpoint.hitCount++

        // 检查最大命中次数
        if (breakpoint.maxHits && breakpoint.hitCount > breakpoint.maxHits) {
          continue
        }

        // 检查条件
        if (breakpoint.condition) {
          try {
            const result = this.evaluateCondition(breakpoint.condition, context)
            if (!result) continue
          } catch (error) {
            console.warn(`断点条件评估失败: ${error}`)
            continue
          }
        }

        return true
      }
    }

    return false
  }

  /**
   * 在节点处暂停
   */
  pauseAtNode(nodeId: string, context: ExecutionContext): void {
    this.state.isPaused = true
    this.state.currentNodeId = nodeId
    this.state.executionStack = this.executor.getExecutionStack()
    this.updateVariables(context)
    this.updateWatchExpressions(context)

    this.debugCallbacks.onBreakpoint?.(nodeId)
  }

  /**
   * 继续执行
   */
  continue(): void {
    this.state.isPaused = false
    this.state.currentNodeId = undefined
    this.stepMode = 'none'
  }

  /**
   * 单步进入
   */
  stepInto(): void {
    this.state.isPaused = false
    this.stepMode = 'into'
  }

  /**
   * 单步跳过
   */
  stepOver(): void {
    this.state.isPaused = false
    this.stepMode = 'over'
  }

  /**
   * 单步跳出
   */
  stepOut(): void {
    this.state.isPaused = false
    this.stepMode = 'out'
  }

  /**
   * 添加监视表达式
   */
  addWatchExpression(expression: string): string {
    const id = `watch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const watch: WatchExpression = {
      id,
      expression
    }
    this.watchExpressions.set(id, watch)
    return id
  }

  /**
   * 移除监视表达式
   */
  removeWatchExpression(watchId: string): void {
    this.watchExpressions.delete(watchId)
  }

  /**
   * 更新变量
   */
  private updateVariables(context: ExecutionContext): void {
    this.state.variables.clear()
    for (const [name, value] of context.variables) {
      this.state.variables.set(name, value)
    }
  }

  /**
   * 更新监视表达式
   */
  private updateWatchExpressions(context: ExecutionContext): void {
    for (const watch of this.watchExpressions.values()) {
      try {
        watch.value = this.evaluateExpression(watch.expression, context)
        watch.error = undefined
      } catch (error) {
        watch.value = undefined
        watch.error = error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 评估条件表达式
   */
  private evaluateCondition(condition: string, context: ExecutionContext): boolean {
    try {
      // 这里应该实现安全的表达式评估
      // 暂时使用简单的字符串比较
      return condition === 'true'
    } catch (error) {
      return false
    }
  }

  /**
   * 评估表达式
   */
  private evaluateExpression(expression: string, context: ExecutionContext): any {
    // 这里应该实现安全的表达式评估
    // 暂时返回变量值
    return context.variables.get(expression)
  }

  /**
   * 设置调试回调
   */
  setCallbacks(callbacks: typeof this.debugCallbacks): void {
    this.debugCallbacks = { ...this.debugCallbacks, ...callbacks }
  }

  /**
   * 获取调试状态
   */
  getState(): DebugState {
    return { ...this.state }
  }

  /**
   * 获取断点列表
   */
  getBreakpoints(): Breakpoint[] {
    return Array.from(this.state.breakpoints.values())
  }

  /**
   * 获取监视表达式列表
   */
  getWatchExpressions(): WatchExpression[] {
    return Array.from(this.watchExpressions.values())
  }

  /**
   * 获取变量值
   */
  getVariable(name: string): any {
    return this.state.variables.get(name)
  }

  /**
   * 设置变量值
   */
  setVariable(name: string, value: any): void {
    this.state.variables.set(name, value)
    this.debugCallbacks.onVariableChange?.(name, value)
  }

  /**
   * 获取执行栈
   */
  getExecutionStack(): string[] {
    return [...this.state.executionStack]
  }

  /**
   * 是否正在调试
   */
  isDebugging(): boolean {
    return this.state.isDebugging
  }

  /**
   * 是否已暂停
   */
  isPaused(): boolean {
    return this.state.isPaused
  }

  /**
   * 获取当前节点ID
   */
  getCurrentNodeId(): string | undefined {
    return this.state.currentNodeId
  }
}

export default DebugSystem
