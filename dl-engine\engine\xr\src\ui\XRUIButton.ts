/**
 * XR UI按钮组件
 * 
 * 在3D空间中创建可交互的按钮
 */

import { Object3D, PlaneGeometry, MeshBasicMaterial, Mesh, Vector3, Color } from 'three'
import { WebLayer3D } from './WebLayer3D'

export interface XRUIButtonOptions {
  /** 按钮文本 */
  text?: string
  /** 按钮宽度（米） */
  width?: number
  /** 按钮高度（米） */
  height?: number
  /** 按钮位置 */
  position?: Vector3
  /** 背景颜色 */
  backgroundColor?: string
  /** 文本颜色 */
  textColor?: string
  /** 悬停背景颜色 */
  hoverBackgroundColor?: string
  /** 按下背景颜色 */
  activeBackgroundColor?: string
  /** 字体大小 */
  fontSize?: number
  /** 是否禁用 */
  disabled?: boolean
  /** 点击回调 */
  onClick?: () => void
  /** 悬停进入回调 */
  onHover?: () => void
  /** 悬停离开回调 */
  onLeave?: () => void
}

/**
 * XR UI按钮类
 */
export class XRUIButton extends Object3D {
  private options: Required<XRUIButtonOptions>
  private webLayer: WebLayer3D
  private buttonElement: HTMLButtonElement
  private isHovered = false
  private isPressed = false

  constructor(options: XRUIButtonOptions = {}) {
    super()

    // 设置默认选项
    this.options = {
      text: 'Button',
      width: 0.3,
      height: 0.08,
      position: new Vector3(0, 0, 0),
      backgroundColor: '#0078d4',
      textColor: '#ffffff',
      hoverBackgroundColor: '#106ebe',
      activeBackgroundColor: '#005a9e',
      fontSize: 16,
      disabled: false,
      onClick: () => {},
      onHover: () => {},
      onLeave: () => {},
      ...options
    }

    this.createButton()
    this.setupInteraction()
    this.updateTransform()
  }

  /**
   * 创建按钮DOM结构
   */
  private createButton(): void {
    // 创建按钮元素
    this.buttonElement = document.createElement('button')
    this.buttonElement.textContent = this.options.text
    this.buttonElement.disabled = this.options.disabled
    
    this.updateButtonStyle()

    // 创建WebLayer3D
    this.webLayer = new WebLayer3D(this.buttonElement, {
      pixelWidth: 512,
      pixelHeight: Math.round(512 * (this.options.height / this.options.width))
    })

    this.webLayer.scale.set(this.options.width, this.options.height, 1)
    this.add(this.webLayer)
  }

  /**
   * 更新按钮样式
   */
  private updateButtonStyle(): void {
    let backgroundColor = this.options.backgroundColor
    
    if (this.options.disabled) {
      backgroundColor = '#666666'
    } else if (this.isPressed) {
      backgroundColor = this.options.activeBackgroundColor
    } else if (this.isHovered) {
      backgroundColor = this.options.hoverBackgroundColor
    }

    this.buttonElement.style.cssText = `
      width: 100%;
      height: 100%;
      background-color: ${backgroundColor};
      color: ${this.options.textColor};
      border: none;
      border-radius: 8px;
      font-size: ${this.options.fontSize}px;
      font-weight: 500;
      cursor: ${this.options.disabled ? 'not-allowed' : 'pointer'};
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      user-select: none;
      outline: none;
    `

    if (this.isPressed) {
      this.buttonElement.style.transform = 'scale(0.95)'
    } else {
      this.buttonElement.style.transform = 'scale(1)'
    }
  }

  /**
   * 设置交互功能
   */
  private setupInteraction(): void {
    // 鼠标事件
    this.buttonElement.addEventListener('click', this.onClick.bind(this))
    this.buttonElement.addEventListener('mouseenter', this.onMouseEnter.bind(this))
    this.buttonElement.addEventListener('mouseleave', this.onMouseLeave.bind(this))
    this.buttonElement.addEventListener('mousedown', this.onMouseDown.bind(this))
    this.buttonElement.addEventListener('mouseup', this.onMouseUp.bind(this))

    // 触摸事件（用于VR控制器）
    this.buttonElement.addEventListener('touchstart', this.onTouchStart.bind(this))
    this.buttonElement.addEventListener('touchend', this.onTouchEnd.bind(this))
  }

  /**
   * 点击事件处理
   */
  private onClick(event: Event): void {
    if (this.options.disabled) return
    event.preventDefault()
    this.options.onClick()
  }

  /**
   * 鼠标进入事件处理
   */
  private onMouseEnter(event: Event): void {
    if (this.options.disabled) return
    this.isHovered = true
    this.updateButtonStyle()
    this.options.onHover()
  }

  /**
   * 鼠标离开事件处理
   */
  private onMouseLeave(event: Event): void {
    this.isHovered = false
    this.isPressed = false
    this.updateButtonStyle()
    this.options.onLeave()
  }

  /**
   * 鼠标按下事件处理
   */
  private onMouseDown(event: Event): void {
    if (this.options.disabled) return
    this.isPressed = true
    this.updateButtonStyle()
  }

  /**
   * 鼠标抬起事件处理
   */
  private onMouseUp(event: Event): void {
    this.isPressed = false
    this.updateButtonStyle()
  }

  /**
   * 触摸开始事件处理
   */
  private onTouchStart(event: Event): void {
    if (this.options.disabled) return
    this.isPressed = true
    this.updateButtonStyle()
  }

  /**
   * 触摸结束事件处理
   */
  private onTouchEnd(event: Event): void {
    this.isPressed = false
    this.updateButtonStyle()
  }

  /**
   * 更新变换
   */
  private updateTransform(): void {
    this.position.copy(this.options.position)
  }

  /**
   * 设置按钮文本
   */
  setText(text: string): void {
    this.options.text = text
    this.buttonElement.textContent = text
  }

  /**
   * 设置按钮位置
   */
  setPosition(position: Vector3): void {
    this.options.position.copy(position)
    this.updateTransform()
  }

  /**
   * 设置按钮大小
   */
  setSize(width: number, height: number): void {
    this.options.width = width
    this.options.height = height
    this.webLayer.scale.set(width, height, 1)
  }

  /**
   * 设置禁用状态
   */
  setDisabled(disabled: boolean): void {
    this.options.disabled = disabled
    this.buttonElement.disabled = disabled
    this.updateButtonStyle()
  }

  /**
   * 设置背景颜色
   */
  setBackgroundColor(color: string): void {
    this.options.backgroundColor = color
    this.updateButtonStyle()
  }

  /**
   * 设置文本颜色
   */
  setTextColor(color: string): void {
    this.options.textColor = color
    this.updateButtonStyle()
  }

  /**
   * 获取按钮元素
   */
  getElement(): HTMLButtonElement {
    return this.buttonElement
  }

  /**
   * 销毁按钮
   */
  dispose(): void {
    if (this.webLayer) {
      this.webLayer.dispose()
    }
    if (this.parent) {
      this.parent.remove(this)
    }
  }
}

export default XRUIButton
