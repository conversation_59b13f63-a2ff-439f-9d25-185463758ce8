/**
 * 选择管理器组件
 * 
 * 管理节点和连接的选择状态
 */

import React, { useCallback, useEffect, useRef } from 'react'
import { Vector2 } from '../types'

/**
 * 选择管理器属性
 */
export interface SelectionManagerProps {
  /** 选中的节点 */
  selectedNodes: string[]
  /** 选中的连接 */
  selectedConnections: string[]
  /** 选择变化回调 */
  onSelectionChange?: (nodeIds: string[], connectionIds: string[]) => void
  /** 多选开始回调 */
  onMultiSelectStart?: (startPos: Vector2) => void
  /** 多选更新回调 */
  onMultiSelectUpdate?: (startPos: Vector2, currentPos: Vector2) => void
  /** 多选结束回调 */
  onMultiSelectEnd?: (startPos: Vector2, endPos: Vector2) => void
}

/**
 * 选择管理器组件
 */
const SelectionManager: React.FC<SelectionManagerProps> = ({
  selectedNodes,
  selectedConnections,
  onSelectionChange,
  onMultiSelectStart,
  onMultiSelectUpdate,
  onMultiSelectEnd
}) => {
  const isMultiSelecting = useRef(false)
  const multiSelectStart = useRef<Vector2>({ x: 0, y: 0 })
  const multiSelectCurrent = useRef<Vector2>({ x: 0, y: 0 })
  
  /**
   * 添加节点到选择
   */
  const addNodeToSelection = useCallback((nodeId: string, exclusive = false) => {
    if (exclusive) {
      onSelectionChange?.([nodeId], [])
    } else {
      const newSelection = selectedNodes.includes(nodeId)
        ? selectedNodes.filter(id => id !== nodeId)
        : [...selectedNodes, nodeId]
      onSelectionChange?.(newSelection, selectedConnections)
    }
  }, [selectedNodes, selectedConnections, onSelectionChange])
  
  /**
   * 添加连接到选择
   */
  const addConnectionToSelection = useCallback((connectionId: string, exclusive = false) => {
    if (exclusive) {
      onSelectionChange?.([], [connectionId])
    } else {
      const newSelection = selectedConnections.includes(connectionId)
        ? selectedConnections.filter(id => id !== connectionId)
        : [...selectedConnections, connectionId]
      onSelectionChange?.(selectedNodes, newSelection)
    }
  }, [selectedNodes, selectedConnections, onSelectionChange])
  
  /**
   * 清除选择
   */
  const clearSelection = useCallback(() => {
    onSelectionChange?.([], [])
  }, [onSelectionChange])
  
  /**
   * 选择所有节点
   */
  const selectAllNodes = useCallback((nodeIds: string[]) => {
    onSelectionChange?.(nodeIds, [])
  }, [onSelectionChange])
  
  /**
   * 反选节点
   */
  const invertNodeSelection = useCallback((allNodeIds: string[]) => {
    const newSelection = allNodeIds.filter(id => !selectedNodes.includes(id))
    onSelectionChange?.(newSelection, selectedConnections)
  }, [selectedNodes, selectedConnections, onSelectionChange])
  
  /**
   * 处理鼠标按下（开始多选）
   */
  const handleMouseDown = useCallback((event: MouseEvent) => {
    // 只在按住Shift或Ctrl时启用多选
    if (event.shiftKey || event.ctrlKey) {
      isMultiSelecting.current = true
      multiSelectStart.current = { x: event.clientX, y: event.clientY }
      multiSelectCurrent.current = { x: event.clientX, y: event.clientY }
      
      onMultiSelectStart?.(multiSelectStart.current)
      event.preventDefault()
    }
  }, [onMultiSelectStart])
  
  /**
   * 处理鼠标移动（更新多选区域）
   */
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (isMultiSelecting.current) {
      multiSelectCurrent.current = { x: event.clientX, y: event.clientY }
      onMultiSelectUpdate?.(multiSelectStart.current, multiSelectCurrent.current)
    }
  }, [onMultiSelectUpdate])
  
  /**
   * 处理鼠标抬起（结束多选）
   */
  const handleMouseUp = useCallback((event: MouseEvent) => {
    if (isMultiSelecting.current) {
      isMultiSelecting.current = false
      onMultiSelectEnd?.(multiSelectStart.current, multiSelectCurrent.current)
    }
  }, [onMultiSelectEnd])
  
  /**
   * 处理键盘事件
   */
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    switch (event.key) {
      case 'Escape':
        clearSelection()
        break
      
      case 'a':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault()
          // 这里需要从外部传入所有节点ID
          // selectAllNodes(allNodeIds)
        }
        break
      
      case 'i':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault()
          // 这里需要从外部传入所有节点ID
          // invertNodeSelection(allNodeIds)
        }
        break
    }
  }, [clearSelection])
  
  /**
   * 绑定事件监听器
   */
  useEffect(() => {
    document.addEventListener('mousedown', handleMouseDown)
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.addEventListener('keydown', handleKeyDown)
    
    return () => {
      document.removeEventListener('mousedown', handleMouseDown)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleMouseDown, handleMouseMove, handleMouseUp, handleKeyDown])
  
  /**
   * 计算选择框
   */
  const getSelectionRect = useCallback(() => {
    if (!isMultiSelecting.current) return null
    
    const start = multiSelectStart.current
    const current = multiSelectCurrent.current
    
    return {
      x: Math.min(start.x, current.x),
      y: Math.min(start.y, current.y),
      width: Math.abs(current.x - start.x),
      height: Math.abs(current.y - start.y)
    }
  }, [])
  
  /**
   * 检查节点是否在选择框内
   */
  const isNodeInSelectionRect = useCallback((nodePos: Vector2, nodeSize: Vector2, selectionRect: any) => {
    if (!selectionRect) return false
    
    return (
      nodePos.x < selectionRect.x + selectionRect.width &&
      nodePos.x + nodeSize.x > selectionRect.x &&
      nodePos.y < selectionRect.y + selectionRect.height &&
      nodePos.y + nodeSize.y > selectionRect.y
    )
  }, [])
  
  /**
   * 获取选择统计信息
   */
  const getSelectionStats = useCallback(() => {
    return {
      nodeCount: selectedNodes.length,
      connectionCount: selectedConnections.length,
      hasSelection: selectedNodes.length > 0 || selectedConnections.length > 0
    }
  }, [selectedNodes, selectedConnections])
  
  // 选择管理器是一个逻辑组件，不渲染UI
  // 但可以暴露一些方法供外部使用
  return null
}

export default SelectionManager
