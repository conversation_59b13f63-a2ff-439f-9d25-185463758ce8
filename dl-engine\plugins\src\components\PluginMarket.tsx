/**
 * 插件市场组件
 * 
 * 提供插件的浏览、搜索、安装和管理功能
 */

import React, { useState, useEffect, useCallback } from 'react'
import { 
  Card, 
  List, 
  Button, 
  Input, 
  Select, 
  Tag, 
  Avatar, 
  Space, 
  Tabs, 
  Modal, 
  Rate,
  Pagination,
  Spin,
  Empty,
  message
} from 'antd'
import { 
  SearchOutlined, 
  DownloadOutlined, 
  DeleteOutlined,
  SettingOutlined,
  StarOutlined,
  UserOutlined,
  AppstoreOutlined,
  CloudDownloadOutlined
} from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import { PluginMetadata, PluginType, PluginStatus } from '../PluginAPI'
import PluginManager from '../PluginManager'

const { Search } = Input
const { Option } = Select
const { TabPane } = Tabs

/**
 * 市场插件信息
 */
interface MarketPlugin extends PluginMetadata {
  /** 下载次数 */
  downloads: number
  /** 评分 */
  rating: number
  /** 评论数 */
  reviews: number
  /** 截图 */
  screenshots?: string[]
  /** 更新时间 */
  updatedAt: string
  /** 文件大小 */
  size: number
  /** 下载URL */
  downloadUrl: string
  /** 是否已安装 */
  installed?: boolean
  /** 安装状态 */
  installStatus?: PluginStatus
}

/**
 * 插件市场属性
 */
export interface PluginMarketProps {
  /** 插件管理器 */
  pluginManager: PluginManager
  /** 是否可见 */
  visible?: boolean
  /** 关闭回调 */
  onClose?: () => void
}

/**
 * 插件市场组件
 */
const PluginMarket: React.FC<PluginMarketProps> = ({
  pluginManager,
  visible = true,
  onClose
}) => {
  const { t } = useTranslation()
  
  // 状态
  const [loading, setLoading] = useState(false)
  const [marketPlugins, setMarketPlugins] = useState<MarketPlugin[]>([])
  const [installedPlugins, setInstalledPlugins] = useState<MarketPlugin[]>([])
  const [searchKeyword, setSearchKeyword] = useState('')
  const [selectedType, setSelectedType] = useState<PluginType | 'all'>('all')
  const [sortBy, setSortBy] = useState<'downloads' | 'rating' | 'updated'>('downloads')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(12)
  const [selectedPlugin, setSelectedPlugin] = useState<MarketPlugin | null>(null)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  
  /**
   * 加载市场插件
   */
  const loadMarketPlugins = useCallback(async () => {
    setLoading(true)
    try {
      // 模拟API调用
      const response = await fetch('/api/plugins/market')
      const plugins = await response.json()
      
      // 检查已安装状态
      const installedIds = new Set(
        pluginManager.getAllPlugins().map(p => p.metadata.id)
      )
      
      const pluginsWithStatus = plugins.map((plugin: MarketPlugin) => ({
        ...plugin,
        installed: installedIds.has(plugin.id),
        installStatus: installedIds.has(plugin.id) 
          ? pluginManager.getPlugin(plugin.id)?.status 
          : undefined
      }))
      
      setMarketPlugins(pluginsWithStatus)
    } catch (error) {
      console.error('加载市场插件失败:', error)
      message.error('加载插件市场失败')
    } finally {
      setLoading(false)
    }
  }, [pluginManager])
  
  /**
   * 加载已安装插件
   */
  const loadInstalledPlugins = useCallback(() => {
    const installed = pluginManager.getAllPlugins().map(plugin => ({
      ...plugin.metadata,
      downloads: 0,
      rating: 0,
      reviews: 0,
      updatedAt: new Date().toISOString(),
      size: 0,
      downloadUrl: '',
      installed: true,
      installStatus: plugin.status
    }))
    
    setInstalledPlugins(installed)
  }, [pluginManager])
  
  /**
   * 初始化
   */
  useEffect(() => {
    if (visible) {
      loadMarketPlugins()
      loadInstalledPlugins()
    }
  }, [visible, loadMarketPlugins, loadInstalledPlugins])
  
  /**
   * 过滤和排序插件
   */
  const filteredPlugins = React.useMemo(() => {
    let filtered = marketPlugins.filter(plugin => {
      // 关键词过滤
      if (searchKeyword) {
        const keyword = searchKeyword.toLowerCase()
        const searchText = [
          plugin.name,
          plugin.description,
          plugin.author,
          ...(plugin.keywords || [])
        ].join(' ').toLowerCase()
        
        if (!searchText.includes(keyword)) {
          return false
        }
      }
      
      // 类型过滤
      if (selectedType !== 'all' && plugin.type !== selectedType) {
        return false
      }
      
      return true
    })
    
    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'downloads':
          return b.downloads - a.downloads
        case 'rating':
          return b.rating - a.rating
        case 'updated':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        default:
          return 0
      }
    })
    
    return filtered
  }, [marketPlugins, searchKeyword, selectedType, sortBy])
  
  /**
   * 分页数据
   */
  const paginatedPlugins = React.useMemo(() => {
    const start = (currentPage - 1) * pageSize
    const end = start + pageSize
    return filteredPlugins.slice(start, end)
  }, [filteredPlugins, currentPage, pageSize])
  
  /**
   * 安装插件
   */
  const handleInstallPlugin = async (plugin: MarketPlugin) => {
    try {
      setLoading(true)
      
      // 下载插件
      const response = await fetch(plugin.downloadUrl)
      const pluginData = await response.blob()
      
      // 这里需要实现插件安装逻辑
      // 1. 解压插件文件
      // 2. 验证插件
      // 3. 安装到插件目录
      // 4. 注册插件
      
      message.success(`插件 ${plugin.name} 安装成功`)
      
      // 刷新列表
      await loadMarketPlugins()
      loadInstalledPlugins()
      
    } catch (error) {
      console.error('安装插件失败:', error)
      message.error(`安装插件失败: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }
  
  /**
   * 卸载插件
   */
  const handleUninstallPlugin = async (plugin: MarketPlugin) => {
    try {
      await pluginManager.unloadPlugin(plugin.id)
      message.success(`插件 ${plugin.name} 卸载成功`)
      
      // 刷新列表
      await loadMarketPlugins()
      loadInstalledPlugins()
      
    } catch (error) {
      console.error('卸载插件失败:', error)
      message.error(`卸载插件失败: ${error.message}`)
    }
  }
  
  /**
   * 启用/禁用插件
   */
  const handleTogglePlugin = async (plugin: MarketPlugin) => {
    try {
      const pluginInstance = pluginManager.getPlugin(plugin.id)
      
      if (pluginInstance?.status === PluginStatus.ACTIVE) {
        await pluginManager.deactivatePlugin(plugin.id)
        message.success(`插件 ${plugin.name} 已禁用`)
      } else {
        await pluginManager.activatePlugin(plugin.id)
        message.success(`插件 ${plugin.name} 已启用`)
      }
      
      // 刷新列表
      loadInstalledPlugins()
      
    } catch (error) {
      console.error('切换插件状态失败:', error)
      message.error(`操作失败: ${error.message}`)
    }
  }
  
  /**
   * 显示插件详情
   */
  const showPluginDetail = (plugin: MarketPlugin) => {
    setSelectedPlugin(plugin)
    setDetailModalVisible(true)
  }
  
  /**
   * 渲染插件卡片
   */
  const renderPluginCard = (plugin: MarketPlugin) => {
    const isInstalled = plugin.installed
    const isActive = plugin.installStatus === PluginStatus.ACTIVE
    
    return (
      <Card
        key={plugin.id}
        hoverable
        style={{ marginBottom: 16 }}
        actions={[
          <Button
            key="detail"
            type="link"
            onClick={() => showPluginDetail(plugin)}
          >
            详情
          </Button>,
          isInstalled ? (
            <Button
              key="toggle"
              type="link"
              onClick={() => handleTogglePlugin(plugin)}
            >
              {isActive ? '禁用' : '启用'}
            </Button>
          ) : (
            <Button
              key="install"
              type="link"
              icon={<DownloadOutlined />}
              onClick={() => handleInstallPlugin(plugin)}
            >
              安装
            </Button>
          ),
          isInstalled && (
            <Button
              key="uninstall"
              type="link"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleUninstallPlugin(plugin)}
            >
              卸载
            </Button>
          )
        ].filter(Boolean)}
      >
        <Card.Meta
          avatar={
            <Avatar 
              src={plugin.icon} 
              icon={<AppstoreOutlined />}
              size={48}
            />
          }
          title={
            <Space>
              {plugin.name}
              {isInstalled && (
                <Tag color={isActive ? 'green' : 'orange'}>
                  {isActive ? '已启用' : '已安装'}
                </Tag>
              )}
            </Space>
          }
          description={
            <div>
              <p>{plugin.description}</p>
              <Space size="small">
                <Tag>{plugin.type}</Tag>
                <span>
                  <UserOutlined /> {plugin.author}
                </span>
                <span>
                  <StarOutlined /> {plugin.rating}
                </span>
                <span>
                  <DownloadOutlined /> {plugin.downloads}
                </span>
              </Space>
            </div>
          }
        />
      </Card>
    )
  }
  
  return (
    <div className="plugin-market">
      {/* 搜索和过滤 */}
      <div className="market-header" style={{ marginBottom: 24 }}>
        <Space size="large" style={{ width: '100%', justifyContent: 'space-between' }}>
          <Search
            placeholder="搜索插件..."
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            style={{ width: 300 }}
            allowClear
          />
          
          <Space>
            <Select
              value={selectedType}
              onChange={setSelectedType}
              style={{ width: 120 }}
            >
              <Option value="all">所有类型</Option>
              {Object.values(PluginType).map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
            
            <Select
              value={sortBy}
              onChange={setSortBy}
              style={{ width: 120 }}
            >
              <Option value="downloads">下载量</Option>
              <Option value="rating">评分</Option>
              <Option value="updated">更新时间</Option>
            </Select>
          </Space>
        </Space>
      </div>
      
      {/* 标签页 */}
      <Tabs defaultActiveKey="market">
        <TabPane tab="插件市场" key="market">
          <Spin spinning={loading}>
            {paginatedPlugins.length > 0 ? (
              <>
                <div className="plugin-grid">
                  {paginatedPlugins.map(renderPluginCard)}
                </div>
                
                <Pagination
                  current={currentPage}
                  pageSize={pageSize}
                  total={filteredPlugins.length}
                  onChange={setCurrentPage}
                  showSizeChanger={false}
                  showQuickJumper
                  showTotal={(total, range) => 
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                  }
                  style={{ textAlign: 'center', marginTop: 24 }}
                />
              </>
            ) : (
              <Empty description="暂无插件" />
            )}
          </Spin>
        </TabPane>
        
        <TabPane tab="已安装" key="installed">
          <div className="plugin-grid">
            {installedPlugins.map(renderPluginCard)}
          </div>
        </TabPane>
      </Tabs>
      
      {/* 插件详情模态框 */}
      <Modal
        title={selectedPlugin?.name}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedPlugin && (
          <div className="plugin-detail">
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div className="plugin-info">
                <Space size="large">
                  <Avatar 
                    src={selectedPlugin.icon} 
                    icon={<AppstoreOutlined />}
                    size={64}
                  />
                  <div>
                    <h3>{selectedPlugin.name}</h3>
                    <p>{selectedPlugin.description}</p>
                    <Space>
                      <Tag>{selectedPlugin.type}</Tag>
                      <span>版本: {selectedPlugin.version}</span>
                      <span>作者: {selectedPlugin.author}</span>
                    </Space>
                  </div>
                </Space>
              </div>
              
              <div className="plugin-stats">
                <Space size="large">
                  <div>
                    <Rate disabled value={selectedPlugin.rating} />
                    <div>{selectedPlugin.rating} 分</div>
                  </div>
                  <div>
                    <div>{selectedPlugin.downloads}</div>
                    <div>下载量</div>
                  </div>
                  <div>
                    <div>{selectedPlugin.reviews}</div>
                    <div>评论</div>
                  </div>
                </Space>
              </div>
              
              {selectedPlugin.screenshots && (
                <div className="plugin-screenshots">
                  <h4>截图</h4>
                  <Space>
                    {selectedPlugin.screenshots.map((screenshot, index) => (
                      <img
                        key={index}
                        src={screenshot}
                        alt={`截图 ${index + 1}`}
                        style={{ width: 200, height: 150, objectFit: 'cover' }}
                      />
                    ))}
                  </Space>
                </div>
              )}
              
              <div className="plugin-actions">
                <Space>
                  {selectedPlugin.installed ? (
                    <>
                      <Button
                        type="primary"
                        onClick={() => handleTogglePlugin(selectedPlugin)}
                      >
                        {selectedPlugin.installStatus === PluginStatus.ACTIVE ? '禁用' : '启用'}
                      </Button>
                      <Button
                        danger
                        onClick={() => handleUninstallPlugin(selectedPlugin)}
                      >
                        卸载
                      </Button>
                    </>
                  ) : (
                    <Button
                      type="primary"
                      icon={<CloudDownloadOutlined />}
                      onClick={() => handleInstallPlugin(selectedPlugin)}
                    >
                      安装插件
                    </Button>
                  )}
                </Space>
              </div>
            </Space>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default PluginMarket
