/**
 * DL-Engine 分割线组件
 * 
 * 基于 Ant Design Divider 的增强版本
 */

import React from 'react'
import { Divider, DividerProps } from 'antd'
import classNames from 'classnames'
import DLIcon from './DLIcon'

/**
 * DL分割线属性
 */
export interface DLDividerProps extends DividerProps {
  /** 分割线图标 */
  icon?: string | React.ReactNode
  /** 分割线变体 */
  variant?: 'solid' | 'dashed' | 'dotted'
  /** 自定义类名 */
  className?: string
}

/**
 * DL分割线组件
 */
const DLDivider: React.FC<DLDividerProps> = ({
  icon,
  variant = 'solid',
  className,
  children,
  ...props
}) => {
  /**
   * 渲染图标
   */
  const renderIcon = () => {
    if (!icon) return null
    
    if (typeof icon === 'string') {
      return <DLIcon name={icon} />
    }
    
    return icon
  }
  
  /**
   * 分割线类名
   */
  const dividerClassName = classNames(
    'dl-divider',
    `dl-divider--${variant}`,
    {
      'dl-divider--with-icon': icon
    },
    className
  )
  
  /**
   * 分割线内容
   */
  const content = icon ? (
    <span className="flex items-center">
      {renderIcon()}
      {children && <span className="ml-2">{children}</span>}
    </span>
  ) : children
  
  return (
    <Divider
      {...props}
      className={dividerClassName}
    >
      {content}
    </Divider>
  )
}

export default DLDivider
